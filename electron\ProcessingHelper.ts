// ProcessingHelper.ts
import { IProcessingHelperDeps } from "./main"
import { GeminiService } from "./GeminiService"
import * as os from 'os'
import * as fs from 'fs'
import * as path from 'path'

// Initialize the Gemini service
const geminiService = new GeminiService()

// List of known proctoring software process names
const PROCTORING_SOFTWARE = [
  'proctorio',
  'proctortrack',
  'respondus',
  'honorlock',
  'examity',
  'proctor360',
  'proctoru',
  'examsoft',
  'examplify',
  'proctorexam',
  'proview',
  'psionline',
  'talview',
  'protortrack',
  'exammonitor',
  'lockdownbrowser',
  'securexam',
  'integrity'
]

export class ProcessingHelper {
  private currentProcessingAbortController: AbortController | null = null;
  private currentExtraProcessingAbortController: AbortController | null = null;
  private proctoringDetectionInterval: NodeJS.Timeout | null = null;

  constructor(private deps: IProcessingHelperDeps) {
    // Start proctoring detection when the helper is initialized
    this.startProctoringDetection();
  }

  /**
   * Detects if any known proctoring software is running on the system
   * Uses different detection methods based on the operating system
   */
  private async detectProctoringSystem(): Promise<boolean> {
    try {
      if (process.platform === 'win32') {
        // On Windows, use tasklist to get running processes
        const { exec } = require('child_process');
        return new Promise((resolve) => {
          exec('tasklist /fo csv /nh', (error: any, stdout: string) => {
            if (error) {
              console.error('Error checking processes:', error);
              resolve(false);
              return;
            }

            const processList = stdout.toLowerCase();
            const detected = PROCTORING_SOFTWARE.some(software =>
              processList.includes(software)
            );

            resolve(detected);
          });
        });
      } else if (process.platform === 'darwin') {
        // On macOS, use ps command
        const { exec } = require('child_process');
        return new Promise((resolve) => {
          exec('ps -ax', (error: any, stdout: string) => {
            if (error) {
              console.error('Error checking processes:', error);
              resolve(false);
              return;
            }

            const processList = stdout.toLowerCase();
            const detected = PROCTORING_SOFTWARE.some(software =>
              processList.includes(software)
            );

            resolve(detected);
          });
        });
      } else {
        // On Linux, use ps command
        const { exec } = require('child_process');
        return new Promise((resolve) => {
          exec('ps -aux', (error: any, stdout: string) => {
            if (error) {
              console.error('Error checking processes:', error);
              resolve(false);
              return;
            }

            const processList = stdout.toLowerCase();
            const detected = PROCTORING_SOFTWARE.some(software =>
              processList.includes(software)
            );

            resolve(detected);
          });
        });
      }
    } catch (error) {
      console.error('Error in proctoring detection:', error);
      return false;
    }
  }

  /**
   * Starts periodic detection of proctoring software
   */
  private startProctoringDetection(): void {
    // Check every 30 seconds for proctoring software
    this.proctoringDetectionInterval = setInterval(async () => {
      const proctoringDetected = await this.detectProctoringSystem();

      if (proctoringDetected) {
        console.log('Proctoring software detected! Taking evasive action...');

        // Take evasive action - hide the window
        const mainWindow = this.deps.getMainWindow();
        if (mainWindow && !mainWindow.isDestroyed()) {
          // Hide the window
          mainWindow.hide();

          // After a random delay, show a minimal version or close completely
          setTimeout(() => {
            // Either close or minimize based on random choice
            if (Math.random() > 0.5) {
              mainWindow.close();
            } else {
              // Show window again after danger has passed
              mainWindow.show();
            }
          }, 5000 + Math.random() * 10000); // Random delay between 5-15 seconds
        }
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Stops the proctoring detection interval
   */
  private stopProctoringDetection(): void {
    if (this.proctoringDetectionInterval) {
      clearInterval(this.proctoringDetectionInterval);
      this.proctoringDetectionInterval = null;
    }
  }

  private async getLanguage(): Promise<string> {
    return "javascript"; // Default to JavaScript if not specified
  }

  public async processScreenshots(): Promise<void> {
    const mainWindow = this.deps.getMainWindow();
    if (!mainWindow) return;

    try {
      // Create a new abort controller for this processing request
      this.currentProcessingAbortController = new AbortController();

      // Get the screenshot paths
      const screenshotPaths = this.deps.getScreenshotQueue();
      if (screenshotPaths.length === 0) {
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      // Notify UI that processing has started
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.INITIAL_START);

      // Process the first screenshot to extract the problem
      const problemInfo = await geminiService.processScreenshot(screenshotPaths[0]);

      // Store the problem info for later use
      this.deps.setProblemInfo(problemInfo);

      // Notify UI that problem has been extracted
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED, problemInfo);

      // Generate a solution using Gemini
      const language = await this.getLanguage();
      console.log(`Using language: ${language} for solution generation`);
      const solutionInfo = await geminiService.generateSolution(problemInfo, language);
      console.log('Solution generated:', JSON.stringify(solutionInfo, null, 2));

      // Notify UI that solution has been generated
      console.log('Sending solution to UI...');
      try {
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS, {
          success: true,
          data: solutionInfo
        });
        console.log('Solution sent to UI with event:', this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS);
      } catch (error) {
        console.error('Error sending solution to UI:', error);
      }

      // Set view to solutions
      this.deps.setView("solutions");

    } catch (error) {
      console.error("Error in processing:", error);

      const mainWindow = this.deps.getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
          "Error processing screenshots. Please try again."
        );
      }
    } finally {
      this.currentProcessingAbortController = null;
    }
  }

  public async processExtraScreenshots(): Promise<void> {
    const mainWindow = this.deps.getMainWindow();
    if (!mainWindow) return;

    try {
      // Create a new abort controller for this processing request
      this.currentExtraProcessingAbortController = new AbortController();

      // Get the extra screenshot paths
      const extraScreenshotPaths = this.deps.getExtraScreenshotQueue();
      if (extraScreenshotPaths.length === 0) {
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      // Get the problem and solution info
      const problemInfo = this.deps.getProblemInfo();
      if (!problemInfo) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
          "No problem information available. Please process screenshots first."
        );
        return;
      }

      // Notify UI that debugging has started
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.DEBUG_START);

      // Get the language
      const language = await this.getLanguage();

      // Debug the solution using Gemini
      const debugResult = await geminiService.debugSolution(
        problemInfo,
        problemInfo, // Use the problem info as solution info for now
        extraScreenshotPaths,
        language
      );

      // Notify UI that debugging is complete
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.DEBUG_SUCCESS, {
        success: true,
        data: debugResult
      });

      // Set hasDebugged flag
      this.deps.setHasDebugged(true);

    } catch (error) {
      console.error("Error in debugging:", error);

      const mainWindow = this.deps.getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
          "Error debugging solution. Please try again."
        );
      }
    } finally {
      this.currentExtraProcessingAbortController = null;
    }
  }



  public cancelOngoingRequests(): void {
    let wasCancelled = false

    if (this.currentProcessingAbortController) {
      this.currentProcessingAbortController.abort()
      this.currentProcessingAbortController = null
      wasCancelled = true
    }

    if (this.currentExtraProcessingAbortController) {
      this.currentExtraProcessingAbortController.abort()
      this.currentExtraProcessingAbortController = null
      wasCancelled = true
    }

    // Reset hasDebugged flag and clear problem info
    this.deps.setHasDebugged(false)
    this.deps.setProblemInfo(null)

    const mainWindow = this.deps.getMainWindow()
    if (wasCancelled && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS)
    }
  }

  /**
   * Process voice input using Gemini API
   * @param transcript The speech transcript to process
   * @param language The programming language to use for code examples
   * @returns The AI response
   */
  public async processVoiceInput(transcript: string, language: string): Promise<string> {
    try {
      // Create a prompt for Gemini that includes the transcript and instructions
      const prompt = `
        I'm a developer working on a coding problem. Here's what I said:

        "${transcript}"

        Please provide a helpful response. If I'm asking about code, use ${language} in your examples.
        If I'm asking about an algorithm or concept, explain it clearly with examples.
        If I'm asking for help with a specific error or bug, suggest possible solutions.
        Keep your response concise but thorough.
      `;

      // Use the Gemini API to process the prompt
      const response = await geminiService.generateTextResponse(prompt);
      return response;
    } catch (error) {
      console.error("Error processing voice input:", error);
      throw error;
    }
  }

  /**
   * Cleanup method to be called when the application is closing
   * Stops all intervals and ongoing processes
   */
  public cleanup(): void {
    // Cancel any ongoing requests
    this.cancelOngoingRequests();

    // Stop proctoring detection
    this.stopProctoringDetection();
  }
}



