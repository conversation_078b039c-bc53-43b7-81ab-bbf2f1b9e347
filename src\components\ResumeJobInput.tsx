import React, { useState, useEffect } from 'react';
import { useToast } from '../contexts/toast';

interface ResumeJobInputProps {
  onSave: (resumeInfo: string, jobDescription: string) => void;
  initialResumeInfo?: string;
  initialJobDescription?: string;
}

const ResumeJobInput: React.FC<ResumeJobInputProps> = ({
  onSave,
  initialResumeInfo = '',
  initialJobDescription = ''
}) => {
  const { showToast } = useToast();
  const [resumeInfo, setResumeInfo] = useState(initialResumeInfo);
  const [jobDescription, setJobDescription] = useState(initialJobDescription);
  const [isExpanded, setIsExpanded] = useState(false);

  // Load saved data from localStorage on component mount
  useEffect(() => {
    try {
      const savedResumeInfo = localStorage.getItem('resumeInfo');
      const savedJobDescription = localStorage.getItem('jobDescription');
      
      if (savedResumeInfo) {
        setResumeInfo(savedResumeInfo);
      }
      
      if (savedJobDescription) {
        setJobDescription(savedJobDescription);
      }
    } catch (error) {
      console.error('Error loading saved resume/job data:', error);
    }
  }, []);

  const handleSave = () => {
    try {
      // Save to localStorage
      localStorage.setItem('resumeInfo', resumeInfo);
      localStorage.setItem('jobDescription', jobDescription);
      
      // Call the onSave callback
      onSave(resumeInfo, jobDescription);
      
      // Show success toast
      showToast('Saved', 'Your resume and job description have been saved', 'success');
      
      // Collapse the panel
      setIsExpanded(false);
    } catch (error) {
      console.error('Error saving resume/job data:', error);
      showToast('Error', 'Failed to save your information', 'error');
    }
  };

  const handleClear = () => {
    setResumeInfo('');
    setJobDescription('');
    localStorage.removeItem('resumeInfo');
    localStorage.removeItem('jobDescription');
    onSave('', '');
    showToast('Cleared', 'Your resume and job description have been cleared', 'neutral');
  };

  return (
    <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700 mb-4">
      <div 
        className="flex items-center justify-between cursor-pointer" 
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
          <h3 className="text-sm font-medium text-gray-300">Resume & Job Description</h3>
        </div>
        <div className="flex items-center">
          {resumeInfo || jobDescription ? (
            <span className="text-xs px-2 py-1 rounded-full bg-green-500/20 text-green-300 mr-2">
              Data Saved
            </span>
          ) : null}
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className={`h-5 w-5 text-gray-400 transition-transform ${isExpanded ? 'transform rotate-180' : ''}`} 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2"
          >
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </div>
      </div>
      
      {isExpanded && (
        <div className="mt-4 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Your Resume Information
            </label>
            <textarea
              className="w-full h-40 bg-gray-700 text-white p-3 rounded-md border border-gray-600 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
              placeholder="Paste your resume information here... Include your skills, experience, education, and any other relevant information."
              value={resumeInfo}
              onChange={(e) => setResumeInfo(e.target.value)}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Job Description
            </label>
            <textarea
              className="w-full h-40 bg-gray-700 text-white p-3 rounded-md border border-gray-600 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
              placeholder="Paste the job description here... Include requirements, responsibilities, and any other relevant details."
              value={jobDescription}
              onChange={(e) => setJobDescription(e.target.value)}
            />
          </div>
          
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleClear}
              className="px-4 py-2 bg-red-500/20 text-red-300 hover:bg-red-500/30 border border-red-500/30 rounded-md text-sm font-medium transition-colors"
            >
              Clear
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-600/20 text-blue-300 hover:bg-blue-600/30 border border-blue-600/30 rounded-md text-sm font-medium transition-colors"
            >
              Save
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResumeJobInput;
