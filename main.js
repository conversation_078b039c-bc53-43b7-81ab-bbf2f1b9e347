// Check if we're in development mode
const isDev = process.env.NODE_ENV === 'development';
const fs = require('fs');
const path = require('path');

// If we're in development mode, wait for the dist-electron directory to be created
if (isDev) {
  const distElectronPath = path.join(__dirname, 'dist-electron');
  const mainJsPath = path.join(distElectronPath, 'main.js');

  // Check if the dist-electron directory exists
  if (!fs.existsSync(distElectronPath)) {
    console.log('dist-electron directory does not exist. Creating it...');
    fs.mkdirSync(distElectronPath, { recursive: true });
  }

  // Check if the main.js file exists
  if (!fs.existsSync(mainJsPath)) {
    console.log('main.js file does not exist. Creating a placeholder...');
    // Create a simple placeholder main.js file
    const placeholderContent = `
      const { app, BrowserWindow } = require('electron');

      function createWindow() {
        const win = new BrowserWindow({
          width: 800,
          height: 600,
          webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
          }
        });

        win.loadURL('http://localhost:54321');
      }

      app.whenReady().then(createWindow);

      app.on('window-all-closed', () => {
        if (process.platform !== 'darwin') {
          app.quit();
        }
      });

      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          createWindow();
        }
      });
    `;

    fs.writeFileSync(mainJsPath, placeholderContent);
  }
}

// Redirect to the actual main.js file in dist-electron
module.exports = require('./dist-electron/main.js');
