console.log("Preload script starting...")
import { context<PERSON><PERSON>, ipc<PERSON><PERSON><PERSON> } from "electron"
const { shell } = require("electron")

// Types for the exposed Electron API
interface ElectronAPI {
  // Subscription and payment methods
  openSubscriptionPortal: (authData: {
    id: string
    email: string
  }) => Promise<{ success: boolean; error?: string }>
  updateContentDimensions: (dimensions: {
    width: number
    height: number
  }) => Promise<void>
  clearStore: () => Promise<{ success: boolean; error?: string }>

  // License and payment methods
  getLicenseStatus: () => Promise<{
    licensed: boolean;
    trialActive: boolean;
    trialDaysLeft: number | null;
    licenseKey: string | null;
    error?: string;
  }>
  activateLicense: (licenseKey: string) => Promise<{ success: boolean; error?: string }>
  checkLicense: () => Promise<{ licensed: boolean; error?: string }>
  onLicenseDialogRequested: (callback: () => void) => () => void

  // Screenshot methods
  getScreenshots: () => Promise<{
    success: boolean
    previews?: Array<{ path: string; preview: string }> | null
    error?: string
  }>
  deleteScreenshot: (
    path: string
  ) => Promise<{ success: boolean; error?: string }>
  onScreenshotTaken: (
    callback: (data: { path: string; preview: string }) => void
  ) => () => void

  // View and processing events
  onResetView: (callback: () => void) => () => void
  onSolutionStart: (callback: () => void) => () => void
  onDebugStart: (callback: () => void) => () => void
  onDebugSuccess: (callback: (data: any) => void) => () => void
  onSolutionError: (callback: (error: string) => void) => () => void
  onProcessingNoScreenshots: (callback: () => void) => () => void
  onProblemExtracted: (callback: (data: any) => void) => () => void
  onSolutionSuccess: (callback: (data: any) => void) => () => void
  onUnauthorized: (callback: () => void) => () => void
  onDebugError: (callback: (error: string) => void) => () => void

  // External and window methods
  openExternal: (url: string) => void
  toggleMainWindow: () => Promise<{ success: boolean; error?: string }>

  // Trigger methods
  triggerScreenshot: () => Promise<{ success: boolean; error?: string }>
  triggerProcessScreenshots: () => Promise<{ success: boolean; error?: string }>
  triggerProcessExtraScreenshots: () => Promise<{ success: boolean; error?: string }>
  triggerReset: () => Promise<{ success: boolean; error?: string }>
  triggerMoveLeft: () => Promise<{ success: boolean; error?: string }>
  triggerMoveRight: () => Promise<{ success: boolean; error?: string }>
  triggerMoveUp: () => Promise<{ success: boolean; error?: string }>
  triggerMoveDown: () => Promise<{ success: boolean; error?: string }>

  // Legacy subscription methods
  onSubscriptionUpdated: (callback: () => void) => () => void
  onSubscriptionPortalClosed: (callback: () => void) => () => void

  // Update methods
  startUpdate: () => Promise<{ success: boolean; error?: string }>
  installUpdate: () => void
  onUpdateAvailable: (callback: (info: any) => void) => () => void
  onUpdateDownloaded: (callback: (info: any) => void) => () => void

  // Credits methods
  decrementCredits: () => Promise<void>
  onCreditsUpdated: (callback: (credits: number) => void) => () => void
  onOutOfCredits: (callback: () => void) => () => void

  // System methods
  getPlatform: () => string

  // Voice processing methods
  processVoiceInput: (transcript: string, language: string, audioBuffer?: Uint8Array, isUserSpeech?: boolean, resumeInfo?: string, jobDescription?: string) => Promise<{ success: boolean; response?: string; error?: string; transcript?: string }>

  // System audio capture methods
  getSystemAudioStream: () => Promise<{ success: boolean; stream?: MediaStream; error?: string }>

  // Resume and job description methods
  updateResumeJobInfo: (resumeInfo: string, jobDescription: string) => Promise<{ success: boolean; error?: string }>

  // Auto system SST methods
  setAutoSystemSST: (enabled: boolean) => Promise<{ success: boolean; enabled?: boolean; error?: string }>
  getAutoSystemSST: () => Promise<{ success: boolean; enabled?: boolean; error?: string }>
  onAutoSystemSSTStatusChanged: (callback: (data: { enabled: boolean }) => void) => () => void
}

export const PROCESSING_EVENTS = {
  //global states
  UNAUTHORIZED: "procesing-unauthorized",
  NO_SCREENSHOTS: "processing-no-screenshots",
  OUT_OF_CREDITS: "out-of-credits",

  //states for generating the initial solution
  INITIAL_START: "initial-start",
  PROBLEM_EXTRACTED: "problem-extracted",
  SOLUTION_SUCCESS: "solution-success",
  INITIAL_SOLUTION_ERROR: "solution-error",
  RESET: "reset",

  //states for processing the debugging
  DEBUG_START: "debug-start",
  DEBUG_SUCCESS: "debug-success",
  DEBUG_ERROR: "debug-error"
} as const

// At the top of the file
console.log("Preload script is running")

const electronAPI = {
  // Subscription and payment methods
  openSubscriptionPortal: async (authData: { id: string; email: string }) => {
    return ipcRenderer.invoke("open-subscription-portal", authData)
  },
  openSettingsPortal: () => ipcRenderer.invoke("open-settings-portal"),
  updateContentDimensions: (dimensions: { width: number; height: number }) =>
    ipcRenderer.invoke("update-content-dimensions", dimensions),
  clearStore: () => ipcRenderer.invoke("clear-store"),

  // License and payment methods
  getLicenseStatus: () => ipcRenderer.invoke("get-license-status"),
  activateLicense: (licenseKey: string) => ipcRenderer.invoke("activate-license", licenseKey),
  checkLicense: () => ipcRenderer.invoke("check-license"),
  onLicenseDialogRequested: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("show-license-dialog", subscription)
    return () => {
      ipcRenderer.removeListener("show-license-dialog", subscription)
    }
  },

  // Screenshot methods
  getScreenshots: () => ipcRenderer.invoke("get-screenshots"),
  deleteScreenshot: (path: string) =>
    ipcRenderer.invoke("delete-screenshot", path),
  toggleMainWindow: async () => {
    console.log("toggleMainWindow called from preload")
    try {
      const result = await ipcRenderer.invoke("toggle-window")
      console.log("toggle-window result:", result)
      return result
    } catch (error) {
      console.error("Error in toggleMainWindow:", error)
      throw error
    }
  },
  // Event listeners
  onScreenshotTaken: (
    callback: (data: { path: string; preview: string }) => void
  ) => {
    const subscription = (_: any, data: { path: string; preview: string }) =>
      callback(data)
    ipcRenderer.on("screenshot-taken", subscription)
    return () => {
      ipcRenderer.removeListener("screenshot-taken", subscription)
    }
  },
  onResetView: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("reset-view", subscription)
    return () => {
      ipcRenderer.removeListener("reset-view", subscription)
    }
  },
  onSolutionStart: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.INITIAL_START, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.INITIAL_START, subscription)
    }
  },
  onDebugStart: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.DEBUG_START, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.DEBUG_START, subscription)
    }
  },
  onDebugSuccess: (callback: (data: any) => void) => {
    console.log('Setting up onDebugSuccess listener')
    const subscription = (_: any, data: any) => {
      console.log('Debug success event received in preload:', data)
      callback(data)
    }
    ipcRenderer.on(PROCESSING_EVENTS.DEBUG_SUCCESS, subscription)
    return () => {
      console.log('Removing onDebugSuccess listener')
      ipcRenderer.removeListener(PROCESSING_EVENTS.DEBUG_SUCCESS, subscription)
    }
  },
  onDebugError: (callback: (error: string) => void) => {
    const subscription = (_: any, error: string) => callback(error)
    ipcRenderer.on(PROCESSING_EVENTS.DEBUG_ERROR, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.DEBUG_ERROR, subscription)
    }
  },
  onSolutionError: (callback: (error: string) => void) => {
    const subscription = (_: any, error: string) => callback(error)
    ipcRenderer.on(PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR, subscription)
    return () => {
      ipcRenderer.removeListener(
        PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
        subscription
      )
    }
  },
  onProcessingNoScreenshots: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.NO_SCREENSHOTS, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.NO_SCREENSHOTS, subscription)
    }
  },
  onOutOfCredits: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.OUT_OF_CREDITS, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.OUT_OF_CREDITS, subscription)
    }
  },
  onProblemExtracted: (callback: (data: any) => void) => {
    const subscription = (_: any, data: any) => callback(data)
    ipcRenderer.on(PROCESSING_EVENTS.PROBLEM_EXTRACTED, subscription)
    return () => {
      ipcRenderer.removeListener(
        PROCESSING_EVENTS.PROBLEM_EXTRACTED,
        subscription
      )
    }
  },
  onSolutionSuccess: (callback: (data: any) => void) => {
    console.log('Setting up onSolutionSuccess listener')
    const subscription = (_: any, data: any) => {
      console.log('Solution success event received in preload:', data)
      callback(data)
    }
    ipcRenderer.on(PROCESSING_EVENTS.SOLUTION_SUCCESS, subscription)
    return () => {
      console.log('Removing onSolutionSuccess listener')
      ipcRenderer.removeListener(
        PROCESSING_EVENTS.SOLUTION_SUCCESS,
        subscription
      )
    }
  },
  onUnauthorized: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.UNAUTHORIZED, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.UNAUTHORIZED, subscription)
    }
  },
  openExternal: (url: string) => shell.openExternal(url),
  triggerScreenshot: () => ipcRenderer.invoke("trigger-screenshot"),
  triggerProcessScreenshots: () =>
    ipcRenderer.invoke("trigger-process-screenshots"),
  triggerProcessExtraScreenshots: () =>
    ipcRenderer.invoke("trigger-process-extra-screenshots"),
  triggerReset: () => ipcRenderer.invoke("trigger-reset"),
  triggerMoveLeft: () => ipcRenderer.invoke("trigger-move-left"),
  triggerMoveRight: () => ipcRenderer.invoke("trigger-move-right"),
  triggerMoveUp: () => ipcRenderer.invoke("trigger-move-up"),
  triggerMoveDown: () => ipcRenderer.invoke("trigger-move-down"),
  onSubscriptionUpdated: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("subscription-updated", subscription)
    return () => {
      ipcRenderer.removeListener("subscription-updated", subscription)
    }
  },
  onSubscriptionPortalClosed: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("subscription-portal-closed", subscription)
    return () => {
      ipcRenderer.removeListener("subscription-portal-closed", subscription)
    }
  },
  onReset: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.RESET, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.RESET, subscription)
    }
  },
  startUpdate: () => ipcRenderer.invoke("start-update"),
  installUpdate: () => ipcRenderer.invoke("install-update"),
  onUpdateAvailable: (callback: (info: any) => void) => {
    const subscription = (_: any, info: any) => callback(info)
    ipcRenderer.on("update-available", subscription)
    return () => {
      ipcRenderer.removeListener("update-available", subscription)
    }
  },
  onUpdateDownloaded: (callback: (info: any) => void) => {
    const subscription = (_: any, info: any) => callback(info)
    ipcRenderer.on("update-downloaded", subscription)
    return () => {
      ipcRenderer.removeListener("update-downloaded", subscription)
    }
  },
  decrementCredits: () => ipcRenderer.invoke("decrement-credits"),
  onCreditsUpdated: (callback: (credits: number) => void) => {
    const subscription = (_event: any, credits: number) => callback(credits)
    ipcRenderer.on("credits-updated", subscription)
    return () => {
      ipcRenderer.removeListener("credits-updated", subscription)
    }
  },
  getPlatform: () => process.platform,

  // Voice processing methods
  processVoiceInput: (transcript: string, language: string, audioBuffer?: Uint8Array, isUserSpeech: boolean = true, resumeInfo?: string, jobDescription?: string) =>
    ipcRenderer.invoke("process-voice-input", transcript, language, audioBuffer, isUserSpeech, resumeInfo, jobDescription),

  // System audio capture methods
  getSystemAudioStream: async () => {
    try {
      console.log('Requesting system audio stream from renderer');
      // This will trigger the display media request handler in main.ts
      const stream = await navigator.mediaDevices.getDisplayMedia({
        audio: true,
        video: true
      });

      console.log('System audio stream obtained successfully');
      return { success: true, stream };
    } catch (error) {
      console.error('Error getting system audio stream:', error);
      return { success: false, error: error.message };
    }
  },

  // Resume and job description methods
  updateResumeJobInfo: (resumeInfo: string, jobDescription: string) => {
    console.log('Updating resume and job info in main process');
    try {
      // Send to main process via IPC
      return ipcRenderer.invoke("update-resume-job-info", resumeInfo, jobDescription);
    } catch (error) {
      console.error('Error updating resume and job info:', error);
      return Promise.resolve({ success: false, error: error.message });
    }
  },

  // Auto system SST methods
  setAutoSystemSST: (enabled: boolean) => {
    console.log('Setting auto system SST to:', enabled);
    return ipcRenderer.invoke("set-auto-system-sst", enabled);
  },

  getAutoSystemSST: () => {
    console.log('Getting auto system SST status');
    return ipcRenderer.invoke("get-auto-system-sst");
  },

  onAutoSystemSSTStatusChanged: (callback: (data: { enabled: boolean }) => void) => {
    console.log('Setting up auto system SST status change listener');
    const subscription = (_: any, data: { enabled: boolean }) => callback(data);
    ipcRenderer.on("auto-system-sst-status", subscription);
    return () => {
      console.log('Removing auto system SST status change listener');
      ipcRenderer.removeListener("auto-system-sst-status", subscription);
    };
  }
} as ElectronAPI

// Before exposing the API
console.log(
  "About to expose electronAPI with methods:",
  Object.keys(electronAPI)
)

// Expose the API
contextBridge.exposeInMainWorld("electronAPI", electronAPI)

console.log("electronAPI exposed to window")

// Add this focus restoration handler
ipcRenderer.on("restore-focus", () => {
  // Try to focus the active element if it exists
  const activeElement = document.activeElement as HTMLElement
  if (activeElement && typeof activeElement.focus === "function") {
    activeElement.focus()
  }
})

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld("electron", {
  ipcRenderer: {
    on: (channel: string, func: (...args: any[]) => void) => {
      const allowedChannels = [
        "auth-callback",
        "speech-transcription",
        "speech-transcription-error",
        "gemini-live-response",
        "gemini-live-error",
        "use-browser-speech-recognition",
        "auto-system-sst-status"
      ];

      if (allowedChannels.includes(channel)) {
        ipcRenderer.on(channel, (_event, ...args) => func(...args));
      }
    },
    removeListener: (channel: string, func: (...args: any[]) => void) => {
      const allowedChannels = [
        "auth-callback",
        "speech-transcription",
        "speech-transcription-error",
        "gemini-live-response",
        "gemini-live-error",
        "use-browser-speech-recognition",
        "auto-system-sst-status"
      ];

      if (allowedChannels.includes(channel)) {
        ipcRenderer.removeListener(channel, (_event, ...args) => func(...args));
      }
    },
    send: (channel: string, ...args: any[]) => {
      const allowedChannels = [
        "update-resume-job-info"
      ];

      if (allowedChannels.includes(channel)) {
        ipcRenderer.send(channel, ...args);
      }
    }
  }
})

