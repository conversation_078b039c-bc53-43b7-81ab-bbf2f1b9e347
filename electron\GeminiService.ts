// GeminiService.ts
import { GoogleGenerativeAI } from "@google/generative-ai";
import fs from "node:fs";
import path from "node:path";
import dotenv from "dotenv";
import { app } from "electron";

// Load environment variables
dotenv.config();

// Default API key if not provided in environment
const DEFAULT_API_KEY = "YOUR_DEFAULT_API_KEY"; // Replace with a default key if needed

// Get API key from environment or use default
const API_KEY = process.env.GEMINI_API_KEY || DEFAULT_API_KEY;

// Log API key status (without revealing the full key)
console.log(`Gemini API Key loaded: ${API_KEY ? 'Yes (starts with ' + API_KEY.substring(0, 4) + '...)' : 'No'}`);

// Initialize the Gemini API client
const genAI = new GoogleGenerativeAI(API_KEY);

export class GeminiService {
  // Model to use for image processing and code generation
  private model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

  /**
   * Process a screenshot to extract a coding problem
   * @param imagePath Path to the screenshot image
   */
  public async processScreenshot(imagePath: string): Promise<any> {
    try {
      console.log(`Processing screenshot: ${imagePath}`);
      // Read the image file
      const imageData = await fs.promises.readFile(imagePath);
      const base64Image = imageData.toString("base64");

      // Create the prompt for Gemini
      const prompt = `
        Analyze this screenshot of a coding problem.
        Extract the problem statement, requirements, and any constraints.
        Format your response as JSON with the following structure:
        {
          "problem": "The full problem statement",
          "requirements": ["list of requirements"],
          "constraints": ["list of constraints"],
          "examples": [{"input": "example input", "output": "example output"}]
        }
      `;

      // Create image object
      const image = {
        inlineData: {
          data: base64Image,
          mimeType: "image/png",
        },
      };

      // Generate content using Gemini
      console.log('Sending request to Gemini API for image processing...');
      const result = await this.model.generateContent([prompt, image]);
      console.log('Received response from Gemini API for image processing.');

      const response = result.response;
      const text = response.text();

      // Try to parse the response as JSON
      try {
        // Extract JSON from the response if it's wrapped in markdown code blocks
        const jsonMatch = text.match(/```json\n([\s\S]*?)\n```/) ||
                         text.match(/```\n([\s\S]*?)\n```/) ||
                         [null, text];

        const jsonStr = jsonMatch[1] || text;
        const parsedResponse = JSON.parse(jsonStr);
        return parsedResponse;
      } catch (parseError) {
        console.error("Error parsing Gemini response as JSON:", parseError);
        // If parsing fails, return the raw text
        return {
          problem: text,
          requirements: [],
          constraints: [],
          examples: []
        };
      }
    } catch (error) {
      console.error("Error processing screenshot with Gemini:", error);
      throw error;
    }
  }

  /**
   * Generate a solution for a coding problem
   * @param problemInfo The problem information extracted from the screenshot
   * @param language The programming language to use for the solution
   */
  public async generateSolution(problemInfo: any, language: string): Promise<any> {
    try {
      console.log(`Generating solution for problem in ${language}`);
      // Create the prompt for Gemini
      const prompt = `
        Generate a solution for the following coding problem in ${language}:

        Problem: ${problemInfo.problem}

        ${problemInfo.requirements ? `Requirements: ${problemInfo.requirements.join(', ')}` : ''}
        ${problemInfo.constraints ? `Constraints: ${problemInfo.constraints.join(', ')}` : ''}
        ${problemInfo.examples ? `Examples: ${JSON.stringify(problemInfo.examples)}` : ''}

        Provide a detailed explanation of your approach, the solution code, and the time and space complexity.
        Format your response as JSON with the following structure:
        {
          "approach": "Detailed explanation of the approach",
          "solution": "The complete solution code",
          "timeComplexity": "Time complexity analysis",
          "spaceComplexity": "Space complexity analysis"
        }
      `;

      // Generate content using Gemini
      console.log('Sending request to Gemini API for solution generation...');
      const result = await this.model.generateContent([prompt]);
      console.log('Received response from Gemini API for solution generation.');

      const response = result.response;
      const text = response.text();

      // Try to parse the response as JSON
      try {
        console.log('Raw Gemini response:', text);
        // Extract JSON from the response if it's wrapped in markdown code blocks
        const jsonMatch = text.match(/```json\n([\s\S]*?)\n```/) ||
                         text.match(/```\n([\s\S]*?)\n```/) ||
                         [null, text];

        const jsonStr = jsonMatch[1] || text;
        console.log('Extracted JSON string:', jsonStr);
        const parsedResponse = JSON.parse(jsonStr);
        console.log('Parsed JSON response:', parsedResponse);

        // Transform the response to match what the Solutions.tsx component expects
        // Extract code from markdown code blocks if present
        let code = parsedResponse.solution || '';
        const codeMatch = code.match(/```(?:[a-z]+)?\n([\s\S]*?)\n```/);
        if (codeMatch && codeMatch[1]) {
          code = codeMatch[1];
        }

        const transformedResponse = {
          code: code,
          thoughts: parsedResponse.approach ? [parsedResponse.approach] : [],
          time_complexity: parsedResponse.timeComplexity || 'Not specified',
          space_complexity: parsedResponse.spaceComplexity || 'Not specified'
        };

        console.log('Transformed response for UI:', transformedResponse);
        return transformedResponse;
      } catch (parseError) {
        console.error("Error parsing Gemini solution response as JSON:", parseError);
        // If parsing fails, return a structured response with the raw text
        // Return a response in the format expected by the Solutions.tsx component
        // Try to extract code from markdown code blocks if present
        let code = text;
        const codeMatch = text.match(/```(?:[a-z]+)?\n([\s\S]*?)\n```/);
        if (codeMatch && codeMatch[1]) {
          code = codeMatch[1];
        }

        return {
          code: code,
          thoughts: ["See solution for details"],
          time_complexity: "Not specified",
          space_complexity: "Not specified"
        };
      }
    } catch (error) {
      console.error("Error generating solution with Gemini:", error);
      throw error;
    }
  }

  /**
   * Debug a solution using additional screenshots
   * @param problemInfo The original problem information
   * @param solutionInfo The generated solution information
   * @param debugImagePaths Paths to additional screenshots for debugging
   * @param language The programming language
   */
  public async debugSolution(
    problemInfo: any,
    solutionInfo: any,
    debugImagePaths: string[],
    language: string
  ): Promise<any> {
    try {
      // Read all debug images
      const debugImages = await Promise.all(
        debugImagePaths.map(async (imagePath) => {
          const imageData = await fs.promises.readFile(imagePath);
          return {
            inlineData: {
              data: imageData.toString("base64"),
              mimeType: "image/png",
            },
          };
        })
      );

      // Create the prompt
      const debugPrompt = `I'm trying to solve this coding problem in ${language}:\n\n` +
        `Problem: ${problemInfo.problem}\n\n` +
        `My current solution is: ${solutionInfo.solution}\n\n` +
        `Here are some additional screenshots that show test cases or errors. ` +
        `Please help me debug my solution. Analyze the screenshots and identify any issues. ` +
        `Provide a corrected solution and explain what was wrong. ` +
        `Format your response as JSON with the following structure: ` +
        `{"analysis": "Analysis of the issues found", "correctedSolution": "The corrected solution code", "explanation": "Explanation of the changes made"}`;

      // Combine prompt with images
      const contentArray = [debugPrompt, ...debugImages];

      // Generate content using Gemini
      const result = await this.model.generateContent(contentArray);

      const response = result.response;
      const text = response.text();

      // Try to parse the response as JSON
      try {
        // Extract JSON from the response if it's wrapped in markdown code blocks
        const jsonMatch = text.match(/```json\n([\s\S]*?)\n```/) ||
                         text.match(/```\n([\s\S]*?)\n```/) ||
                         [null, text];

        const jsonStr = jsonMatch[1] || text;
        const parsedResponse = JSON.parse(jsonStr);
        return parsedResponse;
      } catch (parseError) {
        console.error("Error parsing Gemini debug response as JSON:", parseError);
        // If parsing fails, return a structured response with the raw text
        return {
          analysis: "See explanation for details",
          correctedSolution: text,
          explanation: "Could not parse structured response"
        };
      }
    } catch (error) {
      console.error("Error debugging solution with Gemini:", error);
      throw error;
    }
  }

  /**
   * Generate a text response for voice input
   * @param prompt The prompt to send to Gemini
   * @returns The text response from Gemini
   */
  public async generateTextResponse(prompt: string): Promise<string> {
    try {
      console.log('Sending text prompt to Gemini API:', prompt);

      // Generate content using Gemini
      const result = await this.model.generateContent([prompt]);

      const response = result.response;
      const text = response.text();

      console.log('Received response from Gemini API for text processing.');
      return text;
    } catch (error) {
      console.error("Error generating text response with Gemini:", error);
      throw error;
    }
  }
}
