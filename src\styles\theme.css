:root {
  /* Base colors */
  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-bg-tertiary: #334155;
  --color-bg-accent: #3b82f6;
  --color-bg-accent-hover: #2563eb;
  
  /* Text colors */
  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-tertiary: #94a3b8;
  --color-text-accent: #60a5fa;
  
  /* Border colors */
  --color-border-primary: #334155;
  --color-border-secondary: #475569;
  --color-border-accent: #3b82f6;
  
  /* Special colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* Premium colors */
  --color-premium-start: #f59e0b;
  --color-premium-end: #eab308;
  --color-premium-text: #0f172a;
  
  /* Animation speeds */
  --transition-fast: 150ms;
  --transition-normal: 250ms;
  --transition-slow: 350ms;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Rounded corners */
  --rounded-sm: 0.125rem;
  --rounded-md: 0.375rem;
  --rounded-lg: 0.5rem;
  --rounded-xl: 0.75rem;
  --rounded-full: 9999px;
  
  /* Font sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  
  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: var(--rounded-full);
}

::-webkit-scrollbar-thumb {
  background: var(--color-bg-tertiary);
  border-radius: var(--rounded-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-secondary);
}

/* Global styles */
body {
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  transition: background-color var(--transition-normal) ease-in-out;
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--rounded-md);
  padding: var(--spacing-2) var(--spacing-4);
  font-weight: 500;
  transition: all var(--transition-fast) ease-in-out;
}

.btn-primary {
  background-color: var(--color-bg-accent);
  color: var(--color-text-primary);
}

.btn-primary:hover {
  background-color: var(--color-bg-accent-hover);
}

.btn-secondary {
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-primary);
}

.btn-secondary:hover {
  background-color: var(--color-border-secondary);
}

.btn-premium {
  background: linear-gradient(to right, var(--color-premium-start), var(--color-premium-end));
  color: var(--color-premium-text);
  font-weight: 600;
}

.btn-premium:hover {
  filter: brightness(1.1);
}

/* Card styles */
.card {
  background-color: var(--color-bg-secondary);
  border-radius: var(--rounded-lg);
  border: 1px solid var(--color-border-primary);
  overflow: hidden;
  transition: transform var(--transition-normal) ease-in-out, box-shadow var(--transition-normal) ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Code block styles */
.code-block {
  background-color: var(--color-bg-secondary);
  border-radius: var(--rounded-md);
  border: 1px solid var(--color-border-primary);
  overflow: hidden;
}

.code-block-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--color-bg-tertiary);
  border-bottom: 1px solid var(--color-border-primary);
}

.code-block-content {
  padding: var(--spacing-4);
  overflow-x: auto;
}

/* Badge styles */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--rounded-full);
  font-size: var(--text-xs);
  font-weight: 500;
}

.badge-blue {
  background-color: rgba(59, 130, 246, 0.2);
  color: var(--color-text-accent);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.badge-green {
  background-color: rgba(16, 185, 129, 0.2);
  color: var(--color-success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.badge-yellow {
  background-color: rgba(245, 158, 11, 0.2);
  color: var(--color-warning);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.badge-red {
  background-color: rgba(239, 68, 68, 0.2);
  color: var(--color-error);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Animation classes */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Gradient text */
.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.gradient-blue {
  background-image: linear-gradient(to right, #3b82f6, #60a5fa);
}

.gradient-premium {
  background-image: linear-gradient(to right, var(--color-premium-start), var(--color-premium-end));
}

/* Tooltip styles */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 120px;
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-primary);
  text-align: center;
  border-radius: var(--rounded-md);
  padding: var(--spacing-2);
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity var(--transition-normal);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border-primary);
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Focus styles */
:focus {
  outline: 2px solid var(--color-bg-accent);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .hide-sm {
    display: none;
  }
}

@media (max-width: 768px) {
  .hide-md {
    display: none;
  }
}

@media (max-width: 1024px) {
  .hide-lg {
    display: none;
  }
}
