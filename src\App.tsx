import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import {
  Toast,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport
} from "./components/ui/toast"
import { ToastContext } from "./contexts/toast"
import { useState, useEffect } from "react"
import SubscribedApp from "./_pages/SubscribedApp"
import { ToastMessage } from "./components/ui/toast"

// Create a React Query client with default options
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 0,
      gcTime: Infinity,
      retry: 1,
      refetchOnWindowFocus: false
    },
    mutations: {
      retry: 1
    }
  }
})

function App() {
  const [currentToast, setCurrentToast] = useState<ToastMessage | null>(null)
  const [toastQueue, setToastQueue] = useState<ToastMessage[]>([])
  const [isToastVisible, setIsToastVisible] = useState(false)
  const [currentLanguage, setCurrentLanguage] = useState("javascript")

  // Process the next toast in the queue
  useEffect(() => {
    if (!isToastVisible && toastQueue.length > 0) {
      // Get the next toast from the queue
      const nextToast = toastQueue[0];

      // Remove it from the queue
      setToastQueue(prev => prev.slice(1));

      // Show the toast
      setCurrentToast(nextToast);
      setIsToastVisible(true);

      // Set a timer to hide the toast
      const timer = setTimeout(() => {
        setIsToastVisible(false);
        setCurrentToast(null);
      }, 4000); // Match the duration in the Toast component

      return () => clearTimeout(timer);
    }
  }, [toastQueue, isToastVisible]);

  // Show toast function that adds to the queue
  const showToast = (title: string, description: string, variant: "neutral" | "success" | "error") => {
    console.log(`Adding toast to queue: ${title}`);

    // Add the new toast to the queue
    setToastQueue(prev => [...prev, { title, description, variant }]);
  }

  useEffect(() => {
    // Force unlimited mode
    Object.defineProperties(window, {
      __CREDITS__: {
        value: Number.MAX_SAFE_INTEGER,
        writable: false
      },
      __IS_AUTHENTICATED__: {
        value: true,
        writable: false
      },
      __IS_SUBSCRIBED__: {
        value: true,
        writable: false
      }
    });

    // Disable all network checks
    window.addEventListener('online', (e) => e.stopPropagation());
    window.addEventListener('offline', (e) => e.stopPropagation());
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <ToastProvider>
        <ToastContext.Provider value={{ showToast }}>
          <SubscribedApp
            credits={Number.MAX_SAFE_INTEGER}
            currentLanguage={currentLanguage}
            setLanguage={setCurrentLanguage}
          />

          {/* Toast components */}
          <ToastViewport />
          {currentToast && isToastVisible && (
            <Toast variant={currentToast.variant}>
              <div className="grid gap-1">
                {currentToast.title && <ToastTitle>{currentToast.title}</ToastTitle>}
                {currentToast.description && (
                  <ToastDescription>{currentToast.description}</ToastDescription>
                )}
              </div>
            </Toast>
          )}

          {/* Debug info - remove in production */}
          {/* <div className="fixed bottom-0 left-0 bg-black/50 text-white text-xs p-1 z-50">
            Queue: {toastQueue.length} | Current: {currentToast?.title || 'None'}
          </div> */}
        </ToastContext.Provider>
      </ToastProvider>
    </QueryClientProvider>
  );
}

export default App



