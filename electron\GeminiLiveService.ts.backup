// GeminiLiveService.ts
import { v4 as uuidv4 } from 'uuid';
import { EventEmitter } from 'events';
import { BrowserWindow } from 'electron';
import * as https from 'https';
import * as http from 'http';
import { DeepgramService } from './DeepgramService';
import { GoogleGenerativeAI } from "@google/generative-ai";
import { ContextDataManager } from './ContextDataManager';

/**
 * Service for interacting with the Gemini API with speech-to-text capabilities
 */
export class GeminiLiveService extends EventEmitter {
  private apiKey: string;
  private model: string = 'gemini-1.5-flash-001';
  private baseUrl: string = 'https://generativelanguage.googleapis.com/v1beta';
  private sessionId: string | null = null;
  private sessionActive: boolean = false;
  private mainWindow: BrowserWindow | null = null;
  private responseBuffer: string = '';
  private fallbackApiKey: string | null = null;
  private deepgramService: DeepgramService | null = null;
  private genAI: GoogleGenerativeAI | null = null;
  private genModel: any = null;
  private deepgramApiKey: string | null = null;
  private autoSystemSSTEnabled: boolean = true; // Auto-enable system SST by default
  private lastSystemAudioTimestamp: number = 0;
  private systemAudioSilenceThreshold: number = 2000; // 2 seconds of silence before stopping
  private resumeInfo: string = ''; // Store resume information
  private jobDescription: string = ''; // Store job description
  private contextDataManager: ContextDataManager;

  // --- Context Data Management ---
  // Default context identifiers for backward compatibility
  private static readonly DEFAULT_RESUME_ID = ContextDataManager.DEFAULT_RESUME_ID;
  private static readonly DEFAULT_JOB_ID = ContextDataManager.DEFAULT_JOB_ID;
  // --- End Context Data Management ---
Befoodie.Inc
Remote
₹18,000 - ₹25,000 a month

Location
Remote

Benefits
Pulled from the full job description
Work from home

Full job description
Job Title: Marketing Associate (Full‑Time, Remote – India)
Company: BeFoodie Inc.
Location: Remote (Work from Home – India)
Salary Range: ₹ 18,000 – ₹ 25,000 per month + performance‑based incentives
Start Date: ASAP

About BeFoodie Inc.

BeFoodie Inc. delivers healthy, heat‑and‑eat Indian meals and fresh salads to customers across the Greater Toronto Area. Our mission is to make nutritious eating simple, accessible, and enjoyable. As we expand, we’re searching for a creative, data‑driven Marketing Associate to help grow our brand and customer base.

Key Responsibilities

Campaign Execution: Plan, launch, and optimize digital marketing campaigns (social media, email, PPC).
Content Creation: Write and design compelling posts, newsletters, and blog articles that align with brand tone.
Social Media Management: Manage daily posting, community engagement, and influencer collaborations across Instagram, Facebook, LinkedIn, and more.
Lead Generation: Implement strategies to attract, nurture, and convert leads using CRM and marketing automation tools.
SEO & Web Analytics: Conduct keyword research, optimize web pages, and track performance in Google Analytics and Search Console.
Reporting & KPIs: Monitor campaign metrics (CTR, CPC, conversions, engagement) and present weekly insights to management.
Cross‑Functional Collaboration: Work closely with Sales, Customer Service, and Design to ensure messaging consistency and aligned goals.
Market Research: Analyze competitor activity, customer behavior, and emerging trends to inform marketing strategy.
What We’re Looking For

Bachelor’s degree in Marketing, Business, Communications, or a related field.
1–3 years of hands‑on digital marketing experience (internships count).
Proficiency with social media tools, email marketing platforms, and basic graphic design (Canva, Adobe Suite or similar).
Solid understanding of SEO fundamentals, Google Analytics, and basic HTML/CSS (a plus).
Excellent English writing, editing, and communication skills.
Self‑starter mindset with strong organizational skills and a knack for multitasking.
Ability to work 10 AM – 6 PM EST (to sync with North American hours) and attend virtual meetings in EST.
What You’ll Get

Competitive base salary + uncapped incentives linked to lead and revenue targets.
Full remote flexibility and supportive work culture.
Opportunities for rapid career growth (Senior Associate, Marketing Lead).
Continuous learning resources and professional development programs.
Experience letters and performance bonuses for top achievers.
How to Apply

Click Apply Now on Indeed and attach:

Your latest résumé/CV.
A brief cover letter (max 200 words) summarizing why you’re the best fit for BeFoodie Inc.
(Optional) Links to portfolios, campaigns, or social media accounts you’ve managed.
Shortlisted candidates will receive a Google Meet link for a first‑round interview. We can’t wait to see how your creativity and strategy will help us make healthy eating a global norm!

BeFoodie Inc. is an equal‑opportunity employer. We celebrate diversity and are committed to creating an inclusive environment for all employees.

Job Types: Full-time, Permanent

Pay: ₹18,000.00 - ₹25,000.00 per month

Benefits:

Work from home
Schedule:

Monday to Friday
Night shift
Supplemental Pay:

Commission pay
Performance bonus
Application Question(s):

what experience of marketing do you have?
Please Email us a document of your past projects.
Do you agree that this position is not freelancing and you can not perform any other task while working at our time or we suggest at least 6 hours before we start the shift to keep the productivity?
Education:

Bachelor's (Preferred)
Experience:

Performance marketing: 3 years (Required)
Influencer Marketing: 3 years (Required)
Market analysis: 2 years (Required)
Language:

English (Required)
Shift availability:

Night Shift (Required)
Overnight Shift (Required)
Work Location: Remote
`;
  // --- End Static Context Data ---


  /**
   * Create a new GeminiLiveService
   * @param apiKey The primary API key for Gemini
   * @param mainWindow The main Electron window for sending events
   * @param fallbackApiKey Optional fallback API key
   * @param deepgramApiKey Deepgram API key for speech-to-text
   */
  constructor(
    apiKey: string,
    mainWindow: BrowserWindow | null = null,
    fallbackApiKey: string | null = null,
    deepgramApiKey: string | null = null
  ) {
    super();
    this.apiKey = apiKey;
    this.mainWindow = mainWindow;
    this.fallbackApiKey = fallbackApiKey;
    this.deepgramApiKey = deepgramApiKey;
    this.sessionId = uuidv4();

    console.log('DEBUG: GeminiLiveService initialized');
    console.log('DEBUG: Primary API key exists:', !!this.apiKey);
    console.log('DEBUG: Fallback API key exists:', !!this.fallbackApiKey);
    console.log('DEBUG: Deepgram API key exists:', !!this.deepgramApiKey);

    // Initialize Gemini API
    this.initializeGeminiAPI();

    // Initialize Deepgram service if API key is provided
    if (this.deepgramApiKey) {
      // DeepgramService needs to be imported and have a constructor matching this
      // It also needs a 'ready()' method and a 'transcribeBuffer(buffer: Buffer, isUserSpeech: boolean): Promise<string>' method
      // The DeepgramService class must also have a 'dispose()' method now as called below
      this.deepgramService = new DeepgramService(this.deepgramApiKey, mainWindow);
      console.log('DEBUG: Deepgram service initialized:', !!this.deepgramService);
    } else {
       console.warn('DEBUG: Deepgram API key not provided. Speech-to-text will rely on transcript argument if available.');
    }
  }

  /**
   * Initialize the Gemini API client
   */
  private initializeGeminiAPI(): void {
    try {
      const effectiveApiKey = this.getEffectiveApiKey();
      if (effectiveApiKey) {
        this.genAI = new GoogleGenerativeAI(effectiveApiKey);
        // Note: .getGenerativeModel() can throw an error if the API key is invalid or network issues occur
        this.genModel = this.genAI.getGenerativeModel({ model: this.model });
        console.log('DEBUG: Gemini API client initialized successfully');
        this.sessionActive = true; // Session is active once model client is ready
      } else {
        console.error('DEBUG: No API key available for Gemini API. Cannot initialize.');
        this.sessionActive = false;
      }
    } catch (error) {
      console.error('DEBUG: Error initializing Gemini API client:', error);
       this.sessionActive = false; // Ensure session is not marked active on error
    }
  }



  /**
   * Get the effective API key to use
   * Will use the primary API key if available, otherwise falls back to the regular API key
   */
  private getEffectiveApiKey(): string {
    // Prioritize the direct apiKey property set in the constructor
    if (this.apiKey && this.apiKey.length > 0) {
      return this.apiKey;
    } else if (this.fallbackApiKey && this.fallbackApiKey.length > 0) {
      console.log('DEBUG: Using fallback API key for Gemini Live');
      return this.fallbackApiKey;
    }
    return '';
  }

  /**
   * Initialize a new session
   * This now primarily focuses on ensuring the API key is present and testing connectivity.
   * The Gemini client is initialized in the constructor or processVoiceInput if needed.
   */
  public async initSession(): Promise<boolean> {
    try {
      console.log('=== GEMINI LIVE API DEBUG: Starting initSession ===');

      const effectiveApiKey = this.getEffectiveApiKey();
      console.log(`API Key exists: ${!!effectiveApiKey}`);
      console.log(`API Key length: ${effectiveApiKey ? effectiveApiKey.length : 0}`);

      if (!effectiveApiKey) {
        console.error('DEBUG: No API key available for Gemini Live. Session cannot start.');
        // Notify the renderer process
         if (this.mainWindow) {
            this.mainWindow.webContents.send('gemini-live-status', { connected: false, error: 'No API key available' });
         }
        this.sessionActive = false;
        return false;
      }

       // If genModel is not initialized, try initializing it now
       if (!this.genModel) {
           this.initializeGeminiAPI();
       }

      // If initialization failed (e.g., invalid key), stop here
      if (!this.genModel || !this.sessionActive) {
           console.error('DEBUG: Gemini API client failed to initialize during initSession.');
           if (this.mainWindow) {
             this.mainWindow.webContents.send('gemini-live-status', { connected: false, error: 'Failed to initialize Gemini API client' });
           }
           this.sessionActive = false;
           return false;
      }


      // Optional: Test API connectivity explicitly if needed,
      // but initializeGeminiAPI and the first processVoiceInput call
      // will implicitly test it when generateContent is called.
      // Keeping the test below but it's somewhat redundant with initializeGeminiAPI.
      try {
        console.log('DEBUG: Testing API connectivity (redundant check)...');
        const testUrl = `${this.baseUrl}/models?key=${effectiveApiKey}`;
         // Use fetch with timeout for robustness
        const timeout = 10000; // 10 seconds
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        let testResponse;
        try {
            testResponse = await fetch(testUrl, { signal: controller.signal });
        } catch (fetchError) {
             if (fetchError.name === 'AbortError') {
                 console.error('DEBUG: API connectivity test timed out');
                 throw new Error(`API connectivity test timed out after ${timeout / 1000} seconds.`);
             }
             throw fetchError; // Rethrow other fetch errors
        } finally {
            clearTimeout(timeoutId);
        }

        console.log('DEBUG: API connectivity test result:', testResponse.status, testResponse.statusText);

        if (testResponse.ok) {
          this.sessionActive = true; // Confirm session active if test passes
          if (this.mainWindow) {
            this.mainWindow.webContents.send('gemini-live-status', { connected: true });
          }
          console.log('=== GEMINI LIVE API DEBUG: initSession successful ===');
          return true;
        } else {
          console.error('DEBUG: API connectivity test failed with status:', testResponse.status);
           let errorDetails = `API connectivity test failed with status: ${testResponse.status}`;
           try {
               const errorBody = await testResponse.json();
               errorDetails = errorBody?.error?.message || errorDetails;
           } catch (e) {
               // Ignore JSON parse errors, use default errorDetails
           }

           if (this.mainWindow) {
             this.mainWindow.webContents.send('gemini-live-status', { connected: false, error: errorDetails });
           }
          this.sessionActive = false;
          return false;
        }
      } catch (error) {
        console.error('DEBUG: Error during API connectivity test:', error);
         if (this.mainWindow) {
           this.mainWindow.webContents.send('gemini-live-status', { connected: false, error: `Network error during connectivity test: ${error.message}` });
         }
        this.sessionActive = false;
        return false;
      }
    } catch (error) {
      console.error('DEBUG: Unexpected error initializing Gemini Live API session:', error);

      // Notify the renderer process
      if (this.mainWindow) {
        this.mainWindow.webContents.send('gemini-live-error', {
          error: `Error initializing Gemini Live API session: ${error.message}`,
          details: error.stack
        });
         this.mainWindow.webContents.send('gemini-live-status', { connected: false, error: `Initialization error: ${error.message}` });
      }

      this.sessionActive = false;
      return false;
    }
  }



  /**
   * Process voice input using Deepgram for speech-to-text and Gemini for text processing
   * This method now uses the embedded resume and job description data and aims for
   * a more human-like, candidate-focused response with pause placeholders.
   * @param transcript The speech transcript to process (if already transcribed)
   * @param language The programming language to use for code examples (Note: This param is currently unused in this job interview context)
   * @param audioBuffer Optional audio buffer to transcribe
   * @param isUserSpeech Whether this is user speech (true) or system audio (false)
   * @returns Object containing the AI response and the recognized transcript
   */
  public async processVoiceInput(
    transcript: string = '',
    language: string = 'javascript', // language param is not used in current prompt logic
    audioBuffer?: Buffer,
    isUserSpeech: boolean = true,
    resumeInfo?: string,
    jobDescription?: string
  ): Promise<{ response: string; transcript: string }> {
    try {
      console.log('=== GEMINI LIVE API DEBUG: Starting processVoiceInput ===');
      console.log(`DEBUG: Processing ${isUserSpeech ? 'user speech' : 'system audio'}`);
      console.log('DEBUG: Input transcript:', transcript ? transcript.substring(0, 100) + '...' : '(empty)');
      console.log('DEBUG: isUserSpeech:', isUserSpeech);
      console.log('DEBUG: Resume info provided:', !!resumeInfo);
      console.log('DEBUG: Job description provided:', !!jobDescription);

      // Store resume and job info if provided
      if (resumeInfo) this.resumeInfo = resumeInfo;
      if (jobDescription) this.jobDescription = jobDescription;


      let finalTranscript = transcript;

      // If audio buffer is provided and Deepgram service is available, transcribe it
      if (audioBuffer && this.deepgramService && this.deepgramService.ready()) {
        console.log('DEBUG: Transcribing audio buffer with Deepgram...');
        console.log('DEBUG: Audio buffer size:', audioBuffer.length);
        console.log('DEBUG: Audio source:', isUserSpeech ? 'User speaking' : 'System audio');

        // For system audio, we can still detect audio levels for logging purposes
        // but we won't skip processing regardless of the result
        if (!isUserSpeech) {
          const hasSystemAudio = this.detectSystemAudio(audioBuffer);
          console.log('DEBUG: System audio detection result:', hasSystemAudio);

          // We'll always process the audio, even if no audio is detected
          // This ensures continuous processing of all system audio
          console.log('DEBUG: Processing system audio regardless of detection result');
        }

        // Log the first few bytes to help debug format issues
        if (audioBuffer.length > 20) {
          // Use subarray instead of slice to avoid deprecation warning
          const firstBytes = audioBuffer.subarray(0, 20);
          console.log('DEBUG: Audio buffer first 20 bytes:',
            Array.from(firstBytes)
              .map(b => b.toString(16).padStart(2, '0'))
              .join(' ')
          );
        }

        try {
          // Use different settings based on audio source (Deepgram specific logic might vary)
          // The 'isUserSpeech' flag here determines which Deepgram profile/settings might be used
          const transcriptionResult = await this.deepgramService.transcribeBuffer(audioBuffer, isUserSpeech);
          console.log('DEBUG: Deepgram transcription result:', transcriptionResult);

          if (transcriptionResult) {
            finalTranscript = transcriptionResult;
            console.log(`DEBUG: Successfully transcribed ${isUserSpeech ? 'user speech' : 'system audio'}:`, finalTranscript);

             // Notify the renderer process about the transcription
            if (this.mainWindow) {
              this.mainWindow.webContents.send('speech-transcription', {
                transcript: finalTranscript,
                 isUser: isUserSpeech // Indicate if this was user speech
              });
            }

          } else {
            console.log('DEBUG: Deepgram returned empty transcription.');
             // If Deepgram returns empty, try to rely on the initial transcript argument if available
            if (!finalTranscript) {
                 console.log('DEBUG: Initial transcript was also empty. Deepgram transcription failed.');
                 // Notify the renderer to try browser-based speech recognition if user speech failed
                 if (isUserSpeech && this.mainWindow) {
                   this.mainWindow.webContents.send('use-browser-speech-recognition', {
                     message: 'Deepgram transcription failed, please try browser speech recognition'
                   });
                 }
            }
          }
        } catch (transcriptionError) {
          console.error('DEBUG: Error transcribing audio:', transcriptionError);

          // Notify the renderer process
          if (this.mainWindow) {
            this.mainWindow.webContents.send('speech-transcription-error', {
              error: `Error transcribing audio: ${transcriptionError.message}`,
              details: transcriptionError.stack
            });

            // Also suggest fallback to browser speech recognition if user speech failed
             if (isUserSpeech && this.mainWindow) {
               this.mainWindow.webContents.send('use-browser-speech-recognition', {
                 message: 'Deepgram transcription error, please try browser speech recognition'
               });
             }
          }

          // Continue with any existing transcript even if transcription failed
          console.log('DEBUG: Continuing with existing transcript:', finalTranscript);
        }
      } else if (audioBuffer && !this.deepgramService?.ready()) {
         console.warn('DEBUG: Audio buffer provided but Deepgram service is not ready or not initialized. Skipping transcription.');
         if (!this.deepgramApiKey) {
             console.warn('DEBUG: Deepgram API key was not provided.');
         } else if (this.deepgramService && !this.deepgramService.ready()) {
              console.warn('DEBUG: Deepgram service is initialized but reported not ready.');
         }

          // If this was user speech and we couldn't transcribe, suggest fallback
          if (isUserSpeech && this.mainWindow) {
             this.mainWindow.webContents.send('use-browser-speech-recognition', {
               message: 'Speech recognition service not available or ready. Please try browser speech recognition.'
             });
           }
      }


      // If no transcript is available (neither initial nor from Deepgram), return an error
      if (!finalTranscript) {
        const error = 'No transcript available to process';
        console.error('DEBUG:', error);

        // Notify the renderer process
        if (this.mainWindow) {
          this.mainWindow.webContents.send('gemini-live-error', {
            error: error
          });
        }

        return {
          response: `Sorry, I couldn't process your request. ${error}`,
          transcript: transcript // Return original transcript if final is empty
        };
      }

      console.log('DEBUG: Processing transcript with Gemini API:', finalTranscript);

      // Create a prompt that includes the transcript and the embedded context.
      // Adjust the prompt based on whether it's system audio (interviewer's speech) or user speech (user wants help answering).
      // The prompt instructs the AI to adopt Dhananjay's persona and include [pause] placeholders.
      const prompt = isUserSpeech
        ? `
          You are my personal interview assistant. Assume the persona of Dhananjay, the candidate applying for the Marketing Associate role at BeFoodie Inc. I am the interviewer. I just asked you a question. Your goal is to provide a concise, confident, and natural-sounding answer as if you, Dhananjay, are speaking directly to me, the interviewer. Make it sound like a genuine, well-prepared candidate who is a strong fit for BeFoodie Inc, reflecting real human speech patterns.

          Here is the context I want you to use:

          --- Dhananjay's Resume ---
${GeminiLiveService.DHANANJAY_RESUME}
          --- End Resume ---

          --- BeFoodie Inc. Job Description ---
${GeminiLiveService.BEFOODIE_JOB_DESCRIPTION}
          --- End Job Description ---

          The interviewer (me) just asked: "${finalTranscript}"

          Based on the provided resume and job description, please generate a concise, natural-sounding answer I could give in an interview, speaking as Dhananjay.
          Use simple, clear English. Be specific, drawing *natural* connections between your skills/experience and BeFoodie's needs/mission. Keep the answer brief (1-3 short paragraphs).
          Sound confident, enthusiastic, and professional. Weave in relevant experience from the resume seamlessly where it directly addresses the question or highlights a relevant skill. Focus on *demonstrating* the fit, not just listing qualifications.
          Crucially, insert the placeholder \`[pause]\` after key phrases, sentences, or before transitions where a natural pause might occur for emphasis, thought, or flow. Aim for realistic pauses, not after every few words. Use it sparingly but effectively.
          Do NOT use phrases like "As mentioned in my resume", "My resume states", "In the job description it says", "Based on the context provided", or similar meta-references.
          Do NOT mention that you are an AI or that this is a suggested response.
          Format the answer purely as the response from the candidate. Just provide the answer text, nothing else.
        `
        : `
          You are my personal interview assistant, helping Dhananjay respond to an interviewer. I am the interviewer, and you are guiding Dhananjay. Your task is to process what the interviewer said and provide a suggested response that Dhananjay can use. The suggested response should sound natural, confident, and like a strong candidate.

          Here is the context to inform the suggested answer:

          --- Dhananjay's Resume ---
${GeminiLiveService.DHANANJAY_RESUME}
          --- End Resume ---

          --- BeFoodie Inc. Job Description ---
${GeminiLiveService.BEFOODIE_JOB_DESCRIPTION}
          --- End Job Description ---

          Here's what Dhananjay heard from the interviewer: "${finalTranscript}"

          Your task is to produce a response in two parts:
          1.  First, speaking *as the assistant* to Dhananjay, briefly rephrase the interviewer's question based on the transcript to confirm understanding. Start this part with a phrase like "Okay, based on what was heard..." or "So, if I understand correctly, they're asking about...". This part is just a quick confirmation.
          2.  Second, provide a suggested answer speaking *as Dhananjay* directly to the interviewer. This suggested answer should be concise, confident, natural-sounding, and draw *natural* connections between Dhananjay's resume and the job description, making him sound like a strong fit.

          For the suggested answer (part 2):
          - Use simple, clear English.
          - Be specific, drawing connections between skills/experience and BeFoodie's needs/mission seamlessly.
          - Keep it brief (1-3 short paragraphs).
          - Sound confident, enthusiastic, and professional. Focus on *demonstrating* the fit.
          - Crucially, insert the placeholder \`[pause]\` after key phrases, sentences, or before transitions where a natural pause might occur for emphasis, thought, or flow. Aim for realistic pauses, not after every few words. Use it sparingly but effectively.
          - Do NOT use phrases like "As mentioned in my resume", "My resume states", "In the job description it says", "Based on the context provided", or similar meta-references *in the suggested answer part*.
          - Do NOT mention that you are an AI or that this is a suggested response *in the suggested answer part*.
          - Clearly separate the two parts of your response. Maybe use a marker like "Suggested Answer:" before the second part, or a simple newline.

          Just provide the two-part response text, structured clearly.
        `;

      // Ensure Gemini model client is initialized before calling generateContent
      if (!this.genModel) {
        console.log('DEBUG: Gemini model not initialized, attempting to initialize now...');
        this.initializeGeminiAPI();

        if (!this.genModel) {
          const error = 'Failed to initialize Gemini API after attempt';
          console.error('DEBUG:', error);

          // Notify the renderer process
          if (this.mainWindow) {
            this.mainWindow.webContents.send('gemini-live-error', {
              error: error,
              transcript: finalTranscript
            });
          }

          return {
            response: `Sorry, I couldn't process your request. ${error}`,
            transcript: finalTranscript
          };
        }
      }

      console.log('DEBUG: Sending prompt to Gemini API...');
      // Added generation configuration to ensure more consistent and human-like output
      const generationConfig = {
        // Increased temperature slightly for more natural variation, but kept low enough for focus
        temperature: 0.6,
        topK: 20, // Consider top K probability tokens
        topP: 0.9, // Consider tokens until cumulative probability P is reached
        maxOutputTokens: 600, // Slightly increased token limit just in case
      };

      // Safety settings to filter potentially harmful content
      const safetySettings = [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
      ];


      // Call the generateContent method with the configured model, prompt, and settings
      const result = await this.genModel.generateContent({
        contents: [{ role: "user", parts: [{ text: prompt }] }], // Structure the prompt for the API
        generationConfig,
        safetySettings,
      });
      console.log('DEBUG: Received response from Gemini API');

      const response = result.response;

      // Check for prompt feedback indicating safety issues
      if (response.promptFeedback && response.promptFeedback.blockReason) {
          const blockReason = response.promptFeedback.blockReason;
          const safetyRatings = response.promptFeedback.safetyRatings;
          const blockMessage = `Response blocked due to safety reasons: ${blockReason}. Ratings: ${JSON.stringify(safetyRatings)}`;
          console.error('DEBUG:', blockMessage);

           // Notify the renderer process
          if (this.mainWindow) {
            this.mainWindow.webContents.send('gemini-live-error', {
              error: blockMessage,
              transcript: finalTranscript
            });
          }

          return {
            response: `Sorry, I cannot generate a response for that input due to safety policy violations.`,
            transcript: finalTranscript
          };
      }

      // Get the text from the response
      const text = response.text();

      console.log('DEBUG: Response text length:', text.length);
      console.log('DEBUG: Raw Response Text:', text); // Log the raw response to see pauses

      // Notify the renderer process about the response
      if (this.mainWindow) {
        this.mainWindow.webContents.send('gemini-live-response', {
          text: text, // This text will now include the [pause] placeholders
          transcript: finalTranscript
        });
      }

      return {
        response: text, // The calling code will need to handle the [pause] placeholders
        transcript: finalTranscript
      };
    } catch (error) {
      console.error('DEBUG: Error processing voice input with Gemini Live API:', error);

      // Notify the renderer process
      if (this.mainWindow) {
        this.mainWindow.webContents.send('gemini-live-error', {
          error: `Error processing voice input: ${error.message}`,
          details: error.stack,
          transcript: transcript // Use original transcript in error case
        });
      }

      return {
        response: `Sorry, there was an error processing your request: ${error.message}. Please try again.`,
        transcript: transcript // Use original transcript in error case
      };
    }
  }

  /**
   * Start a real-time voice conversation using WebSockets
   * In this context, it mainly means ensuring the session is initialized.
   */
  public async startVoiceConversation(): Promise<boolean> {
    try {
      // Initialize the session, which includes API key validation and client setup
      const sessionInitialized = await this.initSession();

      // You might also want to signal readiness of Deepgram here if needed for a full real-time flow
      // if (this.deepgramService) {
      //   // Assuming DeepgramService has a method to start listening/connecting if necessary
      //   // await this.deepgramService.startListening();
      // }

      return sessionInitialized;
    } catch (error) {
      console.error('Error starting voice conversation:', error);
       if (this.mainWindow) {
           this.mainWindow.webContents.send('gemini-live-error', {
             error: `Failed to start voice conversation: ${error.message}`,
             details: error.stack
           });
         }
      return false;
    }
  }

  /**
   * End the current session
   */
  public async endSession(): Promise<void> {
    try {
      this.sessionActive = false;
      this.sessionId = null;
      this.responseBuffer = '';
      // Disconnect Deepgram if connected and it has a disconnect method
      // if (this.deepgramService) {
      //   // Assuming DeepgramService has a disconnect method
      //   // this.deepgramService.disconnect();
      // }
      console.log('Gemini Live API session ended');
       if (this.mainWindow) {
            this.mainWindow.webContents.send('gemini-live-status', { connected: false });
         }
    } catch (error) {
      console.error('Error ending session:', error);
       if (this.mainWindow) {
            this.mainWindow.webContents.send('gemini-live-error', {
              error: `Error ending session: ${error.message}`,
              details: error.stack
            });
         }
    }
  }

  /**
   * Enable or disable automatic system speech-to-text
   * @param enabled Whether to enable automatic system SST
   */
  public setAutoSystemSST(enabled: boolean): void {
    this.autoSystemSSTEnabled = enabled;
    console.log(`DEBUG: Auto system SST ${enabled ? 'enabled' : 'disabled'}`);

    // Notify the renderer process
    if (this.mainWindow) {
      this.mainWindow.webContents.send('auto-system-sst-status', {
        enabled: this.autoSystemSSTEnabled
      });
    }
  }

  /**
   * Check if automatic system speech-to-text is enabled
   * @returns Whether automatic system SST is enabled
   */
  public getAutoSystemSSTEnabled(): boolean {
    return this.autoSystemSSTEnabled;
  }

  /**
   * Process system audio to detect speech and automatically start/stop transcription
   * Enhanced with better audio level detection and peak detection
   * @param audioBuffer The audio buffer to analyze
   * @returns True if speech was detected, false otherwise
   */
  public detectSystemAudio(audioBuffer: Buffer): boolean {
    if (!this.autoSystemSSTEnabled || !audioBuffer || audioBuffer.length < 100) {
      return false;
    }

    try {
      // Enhanced audio level detection with peak analysis
      let sum = 0;
      let peakCount = 0;
      let maxConsecutivePeaks = 0;
      let currentConsecutivePeaks = 0;
      const sampleSize = Math.min(2000, audioBuffer.length);
      const step = Math.floor(audioBuffer.length / sampleSize);

      // Sample the buffer at regular intervals
      for (let i = 0; i < sampleSize; i++) {
        const idx = i * step;
        if (idx < audioBuffer.length) {
          // Get absolute value of sample (assuming 8-bit audio centered at 128)
          const sampleValue = Math.abs(audioBuffer[idx] - 128);
          sum += sampleValue;

          // Count peaks above a certain threshold (indicating speech)
          if (sampleValue > 20) { // Adjust this threshold as needed
            peakCount++;
            currentConsecutivePeaks++;
            // Track maximum consecutive peaks (useful for detecting sustained speech)
            if (currentConsecutivePeaks > maxConsecutivePeaks) {
              maxConsecutivePeaks = currentConsecutivePeaks;
            }
          } else {
            currentConsecutivePeaks = 0;
          }
        }
      }

      // Calculate average level and peak ratio
      const avgLevel = sum / sampleSize;
      const peakRatio = (peakCount / sampleSize) * 100;

      // Dynamic threshold based on average level
      // This helps adapt to different audio sources and volumes
      const threshold = Math.max(10, avgLevel * 1.5);

      // Log detailed audio metrics for debugging
      console.log(`DEBUG: Audio detected - avg: ${avgLevel.toFixed(2)}, threshold: ${threshold.toFixed(2)}, peakRatio: ${peakRatio.toFixed(1)}%, maxConsecutive: ${maxConsecutivePeaks}`);

      // Multi-factor detection:
      // 1. Average level above threshold OR
      // 2. Peak ratio above 15% (indicating speech patterns) OR
      // 3. Max consecutive peaks > 10 (indicating sustained speech)
      const hasAudio = avgLevel > threshold || peakRatio > 15 || maxConsecutivePeaks > 10;

      if (hasAudio) {
        // Update timestamp when audio was last detected
        this.lastSystemAudioTimestamp = Date.now();
        return true;
      } else {
        // Check if we've been silent for longer than the threshold
        // This creates a "trailing effect" so transcription doesn't stop immediately
        const silenceTime = Date.now() - this.lastSystemAudioTimestamp;
        return silenceTime < this.systemAudioSilenceThreshold;
      }
    } catch (error) {
      console.error('DEBUG: Error detecting system audio:', error);
      return false;
    }
  }

  /**
   * Clean up resources when the service is no longer needed
   * Includes disposing DeepgramService.
   */
  public dispose(): void {
    console.log('DEBUG: GeminiLiveService disposing...');
    this.endSession(); // End the session gracefully

    // Dispose Deepgram service if it exists and has a dispose method
    if (this.deepgramService && typeof (this.deepgramService as any).dispose === 'function') {
         console.log('DEBUG: Disposing DeepgramService...');
        (this.deepgramService as any).dispose(); // Call dispose method, assuming it exists based on the error fix
        this.deepgramService = null;
    } else if (this.deepgramService) {
        console.warn('DEBUG: DeepgramService exists but does not have a dispose method. Setting to null.');
        this.deepgramService = null;
    }


    // Clean up Gemini client resources if necessary.
    // The GoogleGenerativeAI instance itself doesn't have an explicit dispose method in the public API.
    // We just nullify the references.
    this.genAI = null;
    this.genModel = null;


    // Remove all listeners to prevent memory leaks
    this.removeAllListeners();

    console.log('DEBUG: GeminiLiveService disposed');
  }

  /**
   * Check if the service is ready to use (API key available)
   */
  public isReady(): boolean {
    return !!this.getEffectiveApiKey();
  }

  /**
   * Check if the Gemini session is active (client initialized and marked active)
   */
  public isConnected(): boolean {
    return this.sessionActive && !!this.genModel;
  }



  /**
   * Update resume and job description information
   * @param resumeInfo The resume information
   * @param jobDescription The job description
   * @returns Success status
   */
  public async updateResumeJobInfo(resumeInfo: string, jobDescription: string): Promise<boolean> {
    try {
      console.log('DEBUG: Updating resume and job info');
      console.log('DEBUG: Resume info length:', resumeInfo?.length || 0);
      console.log('DEBUG: Job description length:', jobDescription?.length || 0);

      this.resumeInfo = resumeInfo || '';
      this.jobDescription = jobDescription || '';

      return true;
    } catch (error) {
      console.error('DEBUG: Error updating resume and job info:', error);
      return false;
    }
  }

  /**
   * Validate the API key by making a test request to the models endpoint.
   * @returns Promise resolving to true if the API key is valid and allows access to models, false otherwise.
   */
  public async validateApiKey(): Promise<boolean> {
    try {
      console.log('=== GEMINI LIVE API DEBUG: Validating API key ===');

      // Get the effective API key
      const effectiveApiKey = this.getEffectiveApiKey();

      if (!effectiveApiKey) {
        console.error('DEBUG: No API key provided for validation.');
        // Notify the renderer process
         if (this.mainWindow) {
          this.mainWindow.webContents.send('gemini-api-status', {
            valid: false,
            error: 'No API key provided',
            code: 'NO_API_KEY'
          });
        }
        return false;
      }

      console.log('DEBUG: Testing API key with models endpoint');
      const testUrl = `${this.baseUrl}/models?key=${effectiveApiKey}`;

      console.log('DEBUG: Sending test request to:', this.baseUrl + '/models');
      // Use fetch with timeout for robustness
      const timeout = 15000; // Increased timeout slightly
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      let response;
      try {
          response = await fetch(testUrl, { signal: controller.signal });
      } catch (fetchError: any) { // Explicitly type fetchError as any for checking name property
          if (fetchError.name === 'AbortError') {
              console.error('DEBUG: API key validation request timed out');
               if (this.mainWindow) {
                this.mainWindow.webContents.send('gemini-api-status', {
                  valid: false,
                  error: `API validation request timed out after ${timeout / 1000} seconds. Check your internet connection.`,
                  isNetworkError: true,
                  code: 'TIMEOUT'
                });
              }
              return false;
          }
          console.error('DEBUG: Fetch error during API key validation:', fetchError);
          throw fetchError; // Rethrow other fetch errors
      } finally {
          clearTimeout(timeoutId);
      }

      console.log('DEBUG: Test request status:', response.status, response.statusText);

      // Attempt to parse JSON even on error for more details from the API
      let data: any = null;
      try {
         data = await response.json();
      } catch (jsonError) {
         console.warn('DEBUG: Failed to parse response JSON:', jsonError);
         // If JSON parsing fails, data remains null. Continue with status check.
      }


      if (response.ok) {
        console.log('DEBUG: API key is valid.');
        console.log('DEBUG: Available models:', data?.models ? data.models.length : 'N/A (JSON parse failed)');

        // Check if our specific model is available
        const modelAvailable = data?.models && data.models.some((model: any) =>
          model.name === `models/${this.model}` || model.name.endsWith(`/${this.model}`) // Check for both 'models/model-name' and 'model-name' formats
        );

        console.log('DEBUG: Required model available:', modelAvailable);

        // Notify the renderer process
        if (this.mainWindow) {
          this.mainWindow.webContents.send('gemini-api-status', {
            valid: true,
            modelAvailable: modelAvailable,
            message: modelAvailable ? 'API key is valid and required model is available.' : `API key is valid, but required model '${this.model}' may not be listed or accessible.`
          });
        }

        return true;
      } else {
        console.error('DEBUG: API key validation failed:', data?.error || 'Unknown API error');

        // Notify the renderer process
        if (this.mainWindow) {
          this.mainWindow.webContents.send('gemini-api-status', {
            valid: false,
            error: data?.error?.message || `API request failed with status ${response.status}`,
            code: data?.error?.code || response.status, // Use status code if API code isn't available
            details: data // Include raw data for more debugging info
          });
        }

        return false;
      }
    } catch (error: any) { // Explicitly type error as any
      console.error('DEBUG: Unexpected error during API key validation:', error);

      // Notify the renderer process
      if (this.mainWindow) {
        this.mainWindow.webContents.send('gemini-api-status', {
          valid: false,
          error: `Network or unexpected error during validation: ${error.message}`,
          isNetworkError: true,
          code: 'NETWORK_ERROR',
           details: error.stack
        });
      }

      return false;
    }
  }
}