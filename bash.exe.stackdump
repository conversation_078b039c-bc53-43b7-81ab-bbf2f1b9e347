Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD05D00000 ntdll.dll
7FFD04130000 KERNEL32.DLL
7FFD03060000 KERNELBASE.dll
7FFD04F80000 USER32.dll
7FFD03580000 win32u.dll
000210040000 msys-2.0.dll
7FFD04F00000 GDI32.dll
7FFD02E90000 gdi32full.dll
7FFD034D0000 msvcp_win.dll
7FFD035B0000 ucrtbase.dll
7FFD03DC0000 advapi32.dll
7FFD05240000 msvcrt.dll
7FFD05460000 sechost.dll
7FFD03CA0000 RPCRT4.dll
7FFD02480000 CRYPTBASE.DLL
7FFD03430000 bcryptPrimitives.dll
7FFD04F40000 IMM32.DLL
