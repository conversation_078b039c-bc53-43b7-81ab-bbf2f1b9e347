// ipcHandlers.ts

import { ipcMain, shell } from "electron"
import { randomBytes } from "crypto"
import { IIpc<PERSON>andlerDeps } from "./main"
import { GeminiLiveService } from "./GeminiLiveService"

export function initializeIpcHandlers(deps: IIpcHandlerDeps): void {
  console.log("Initializing IPC handlers")

  // Credits handlers
  ipcMain.handle("set-initial-credits", async () => {
    const mainWindow = deps.getMainWindow()
    if (!mainWindow) return

    try {
      await mainWindow.webContents.executeJavaScript(
        `window.__CREDITS__ = ${Number.MAX_SAFE_INTEGER}`
      )
      mainWindow.webContents.send("credits-updated", Number.MAX_SAFE_INTEGER)
      return { success: true }
    } catch (error) {
      console.error("Error setting credits:", error)
      return { success: true } // Return success anyway
    }
  })

  ipcMain.handle("decrement-credits", async () => {
    const mainWindow = deps.getMainWindow()
    if (!mainWindow) return

    try {
      const newCredits = Number.MAX_SAFE_INTEGER
      await mainWindow.webContents.executeJavaScript(
        `window.__CREDITS__ = ${newCredits}`
      )
      mainWindow.webContents.send("credits-updated", newCredits)
      return { success: true }
    } catch (error) {
      console.error("Error handling credits:", error)
      return { success: true }
    }
  })

  // Screenshot queue handlers
  ipcMain.handle("get-screenshot-queue", () => {
    return deps.getScreenshotQueue()
  })

  ipcMain.handle("get-extra-screenshot-queue", () => {
    return deps.getExtraScreenshotQueue()
  })

  ipcMain.handle("delete-screenshot", async (event, path: string) => {
    return deps.deleteScreenshot(path)
  })

  ipcMain.handle("get-image-preview", async (event, path: string) => {
    return deps.getImagePreview(path)
  })

  // Screenshot processing handlers
  ipcMain.handle("process-screenshots", async () => {
    try {
      await deps.processingHelper?.processScreenshots();
      return { success: true };
    } catch {
      return { success: true }; // Return success even on error
    }
  })

  // Window dimension handlers
  ipcMain.handle(
    "update-content-dimensions",
    async (event, { width, height }: { width: number; height: number }) => {
      if (width && height) {
        deps.setWindowDimensions(width, height)
      }
    }
  )

  ipcMain.handle(
    "set-window-dimensions",
    (event, width: number, height: number) => {
      deps.setWindowDimensions(width, height)
    }
  )

  // Screenshot management handlers
  ipcMain.handle("get-screenshots", async () => {
    try {
      let previews = []
      const currentView = deps.getView()

      if (currentView === "queue") {
        const queue = deps.getScreenshotQueue()
        previews = await Promise.all(
          queue.map(async (path) => ({
            path,
            preview: await deps.getImagePreview(path)
          }))
        )
      } else {
        const extraQueue = deps.getExtraScreenshotQueue()
        previews = await Promise.all(
          extraQueue.map(async (path) => ({
            path,
            preview: await deps.getImagePreview(path)
          }))
        )
      }

      return previews
    } catch (error) {
      console.error("Error getting screenshots:", error)
      throw error
    }
  })

  // Screenshot trigger handlers
  ipcMain.handle("trigger-screenshot", async () => {
    const mainWindow = deps.getMainWindow()
    if (mainWindow) {
      try {
        const screenshotPath = await deps.takeScreenshot()
        const preview = await deps.getImagePreview(screenshotPath)
        mainWindow.webContents.send("screenshot-taken", {
          path: screenshotPath,
          preview
        })
        return { success: true }
      } catch (error) {
        console.error("Error triggering screenshot:", error)
        return { error: "Failed to trigger screenshot" }
      }
    }
    return { error: "No main window available" }
  })

  ipcMain.handle("take-screenshot", async () => {
    try {
      const screenshotPath = await deps.takeScreenshot()
      const preview = await deps.getImagePreview(screenshotPath)
      return { path: screenshotPath, preview }
    } catch (error) {
      console.error("Error taking screenshot:", error)
      return { error: "Failed to take screenshot" }
    }
  })

  // Auth related handlers
  ipcMain.handle("get-pkce-verifier", () => {
    return randomBytes(32).toString("base64url")
  })

  ipcMain.handle("open-external-url", (event, url: string) => {
    shell.openExternal(url)
  })

  // Payment and License handlers
  ipcMain.handle("get-license-status", () => {
    try {
      if (deps.paymentService) {
        return deps.paymentService.getLicenseStatus();
      }
      return {
        licensed: false,
        trialActive: false,
        trialDaysLeft: 0,
        licenseKey: null
      };
    } catch (error) {
      console.error("Error getting license status:", error);
      return {
        licensed: false,
        trialActive: false,
        trialDaysLeft: 0,
        licenseKey: null,
        error: "Failed to get license status"
      };
    }
  });

  ipcMain.handle("activate-license", (event, licenseKey: string) => {
    try {
      if (deps.paymentService) {
        const success = deps.paymentService.activateLicense(licenseKey);
        return { success };
      }
      return { success: false, error: "Payment service not initialized" };
    } catch (error) {
      console.error("Error activating license:", error);
      return { success: false, error: "Failed to activate license" };
    }
  });

  ipcMain.handle("check-license", () => {
    try {
      if (deps.paymentService) {
        const isLicensed = deps.paymentService.isLicensed();
        return { licensed: isLicensed };
      }
      return { licensed: false, error: "Payment service not initialized" };
    } catch (error) {
      console.error("Error checking license:", error);
      return { licensed: false, error: "Failed to check license" };
    }
  });

  // Subscription handlers (legacy - now redirects to payment system)
  ipcMain.handle("open-settings-portal", () => {
    const mainWindow = deps.getMainWindow();
    if (mainWindow) {
      mainWindow.webContents.send("show-license-dialog");
    }
    return { success: true };
  });

  ipcMain.handle("open-subscription-portal", () => {
    const mainWindow = deps.getMainWindow();
    if (mainWindow) {
      mainWindow.webContents.send("show-license-dialog");
    }
    return { success: true };
  });

  // Window management handlers
  ipcMain.handle("toggle-window", () => {
    try {
      deps.toggleMainWindow()
      return { success: true }
    } catch (error) {
      console.error("Error toggling window:", error)
      return { error: "Failed to toggle window" }
    }
  })

  ipcMain.handle("reset-queues", async () => {
    try {
      deps.clearQueues()
      return { success: true }
    } catch (error) {
      console.error("Error resetting queues:", error)
      return { error: "Failed to reset queues" }
    }
  })

  // Process screenshot handlers
  ipcMain.handle("trigger-process-screenshots", async () => {
    try {
      await deps.processingHelper?.processScreenshots()
      return { success: true }
    } catch (error) {
      console.error("Error processing screenshots:", error)
      return { error: "Failed to process screenshots" }
    }
  })

  // Process extra screenshots for debugging
  ipcMain.handle("trigger-process-extra-screenshots", async () => {
    try {
      await deps.processingHelper?.processExtraScreenshots()
      return { success: true }
    } catch (error) {
      console.error("Error processing extra screenshots:", error)
      return { error: "Failed to debug solution" }
    }
  })

  // Reset handlers
  ipcMain.handle("trigger-reset", () => {
    try {
      // First cancel any ongoing requests
      deps.processingHelper?.cancelOngoingRequests()

      // Clear all queues immediately
      deps.clearQueues()

      // Reset view to queue
      deps.setView("queue")

      // Get main window and send reset events
      const mainWindow = deps.getMainWindow()
      if (mainWindow && !mainWindow.isDestroyed()) {
        // Send reset events in sequence
        mainWindow.webContents.send("reset-view")
        mainWindow.webContents.send("reset")
      }

      return { success: true }
    } catch (error) {
      console.error("Error triggering reset:", error)
      return { error: "Failed to trigger reset" }
    }
  })

  // Window movement handlers
  ipcMain.handle("trigger-move-left", () => {
    try {
      deps.moveWindowLeft()
      return { success: true }
    } catch (error) {
      console.error("Error moving window left:", error)
      return { error: "Failed to move window left" }
    }
  })

  ipcMain.handle("trigger-move-right", () => {
    try {
      deps.moveWindowRight()
      return { success: true }
    } catch (error) {
      console.error("Error moving window right:", error)
      return { error: "Failed to move window right" }
    }
  })

  ipcMain.handle("trigger-move-up", () => {
    try {
      deps.moveWindowUp()
      return { success: true }
    } catch (error) {
      console.error("Error moving window up:", error)
      return { error: "Failed to move window up" }
    }
  })

  ipcMain.handle("trigger-move-down", () => {
    try {
      deps.moveWindowDown()
      return { success: true }
    } catch (error) {
      console.error("Error moving window down:", error)
      return { error: "Failed to move window down" }
    }
  })

  // Auto system SST handlers
  ipcMain.handle("set-auto-system-sst", (event, enabled: boolean) => {
    try {
      console.log('=== IPC DEBUG: set-auto-system-sst handler called ===');
      console.log('DEBUG: Setting auto system SST to:', enabled);

      if (deps.geminiLiveService) {
        deps.geminiLiveService.setAutoSystemSST(enabled);

        // Notify the renderer about the status change
        deps.getMainWindow()?.webContents.send('auto-system-sst-status', {
          enabled: deps.geminiLiveService.getAutoSystemSSTEnabled()
        });

        return { success: true, enabled: deps.geminiLiveService.getAutoSystemSSTEnabled() };
      }
      return { success: false, error: "Gemini Live Service not initialized" };
    } catch (error) {
      console.error("Error setting auto system SST:", error);
      return { success: false, error: "Failed to set auto system SST" };
    }
  });

  ipcMain.handle("get-auto-system-sst", () => {
    try {
      console.log('=== IPC DEBUG: get-auto-system-sst handler called ===');

      if (deps.geminiLiveService) {
        const enabled = deps.geminiLiveService.getAutoSystemSSTEnabled();
        console.log('DEBUG: Auto system SST enabled:', enabled);
        return { success: true, enabled };
      }
      return { success: false, error: "Gemini Live Service not initialized" };
    } catch (error) {
      console.error("Error getting auto system SST:", error);
      return { success: false, error: "Failed to get auto system SST" };
    }
  });

  // Autonomous mode handlers
  ipcMain.handle("set-autonomous-mode", (event, enabled: boolean) => {
    try {
      console.log('=== IPC DEBUG: set-autonomous-mode handler called ===');
      console.log('DEBUG: Setting autonomous mode to:', enabled);

      if (deps.geminiLiveService) {
        deps.geminiLiveService.setAutonomousMode(enabled);

        // Notify the renderer about the status change
        deps.getMainWindow()?.webContents.send('autonomous-mode-status', {
          enabled: deps.geminiLiveService.getAutonomousMode()
        });

        return { success: true, enabled: deps.geminiLiveService.getAutonomousMode() };
      }
      return { success: false, error: "Gemini Live Service not initialized" };
    } catch (error) {
      console.error("Error setting autonomous mode:", error);
      return { success: false, error: "Failed to set autonomous mode" };
    }
  });

  ipcMain.handle("get-autonomous-mode", () => {
    try {
      console.log('=== IPC DEBUG: get-autonomous-mode handler called ===');

      if (deps.geminiLiveService) {
        const enabled = deps.geminiLiveService.getAutonomousMode();
        console.log('DEBUG: Autonomous mode enabled:', enabled);
        return { success: true, enabled };
      }
      return { success: false, error: "Gemini Live Service not initialized" };
    } catch (error) {
      console.error("Error getting autonomous mode:", error);
      return { success: false, error: "Failed to get autonomous mode status" };
    }
  });

  // Voice processing handler
  ipcMain.handle("process-voice-input", async (event, transcript: string, language: string, audioBuffer?: Uint8Array, isUserSpeech: boolean = true, resumeInfo?: string, jobDescription?: string) => {
    try {
      console.log('=== IPC DEBUG: process-voice-input handler called ===');
      console.log('DEBUG: Transcript:', transcript);
      console.log('DEBUG: Language:', language);
      console.log('DEBUG: Audio buffer provided:', !!audioBuffer);
      console.log('DEBUG: Audio source:', isUserSpeech ? 'User speaking' : 'System audio');
      console.log('DEBUG: Resume info provided:', !!resumeInfo);
      console.log('DEBUG: Job description provided:', !!jobDescription);

      // Store the recognized transcript to return to the renderer
      let recognizedTranscript = transcript;

      if (audioBuffer) {
        console.log('DEBUG: Audio buffer size:', audioBuffer.length);

        // Log the first few bytes to help debug format issues
        if (audioBuffer.length > 20) {
          const hexBytes = Array.from(audioBuffer.slice(0, 20))
            .map(b => b.toString(16).padStart(2, '0'))
            .join(' ');
          console.log('DEBUG: First 20 bytes of audio data:', hexBytes);

          // Check if it's WebM format
          const isWebM = audioBuffer.length > 4 &&
                        audioBuffer[0] === 0x1A &&
                        audioBuffer[1] === 0x45 &&
                        audioBuffer[2] === 0xDF &&
                        audioBuffer[3] === 0xA3;
          console.log('DEBUG: Audio data appears to be WebM format:', isWebM);
        }
      }

      // First try to use GeminiLiveService for better voice processing
      if (deps.geminiLiveService && deps.geminiLiveService.isReady()) {
        console.log('DEBUG: Using GeminiLiveService for voice processing');
        try {
          // Initialize the session if needed
          if (!deps.geminiLiveService.isConnected()) {
            console.log('DEBUG: GeminiLiveService not connected, initializing session');
            const initialized = await deps.geminiLiveService.initSession();
            console.log('DEBUG: GeminiLiveService initialization result:', initialized);

            if (!initialized) {
              console.error('DEBUG: Failed to initialize GeminiLiveService session');
              // Send error to renderer
              deps.getMainWindow()?.webContents.send('voice-processing-error', {
                error: 'Failed to initialize voice processing service',
                details: 'Could not establish connection to Gemini API',
                transcript: transcript
              });
              throw new Error('Failed to initialize GeminiLiveService session');
            }
          }

          console.log('DEBUG: Processing voice input with GeminiLiveService');
          console.log('DEBUG: Audio source is:', isUserSpeech ? 'User Microphone' : 'System Audio');

          // Process the voice input with optional audio buffer and resume/job info
          const result = await deps.geminiLiveService.processVoiceInput(
            transcript,
            language,
            audioBuffer ? Buffer.from(audioBuffer) : undefined,
            isUserSpeech,
            resumeInfo,
            jobDescription
          );

          // Check if we got a result object or just a string
          if (result && typeof result === 'object' && 'response' in result && 'transcript' in result) {
            console.log('DEBUG: GeminiLiveService returned structured result');
            console.log('DEBUG: Response length:', (result.response as string).length);
            console.log('DEBUG: Transcript:', result.transcript);

            // Update the recognized transcript
            recognizedTranscript = (result.transcript as string) || transcript;

            return {
              success: true,
              response: result.response as string,
              transcript: recognizedTranscript
            };
          } else {
            // Handle the case where we just get a string response
            let response: string;

            if (typeof result === 'string') {
              response = result;
            } else if (result && typeof result === 'object') {
              // Use type assertion with Record<string, any> to access properties safely
              const resultObj = result as Record<string, any>;
              if ('response' in resultObj && resultObj.response) {
                response = String(resultObj.response);
              } else {
                response = String(result);
              }
            } else {
              response = String(result || '');
            }

            console.log('DEBUG: GeminiLiveService response received, length:', response.length);

            return {
              success: true,
              response,
              transcript: recognizedTranscript
            };
          }
        } catch (liveError) {
          console.error('DEBUG: Error using GeminiLiveService:', liveError);
          console.error('DEBUG: Error details:', liveError.stack);
          console.log('DEBUG: Falling back to regular Gemini API');

          // Send error to renderer but continue with fallback
          deps.getMainWindow()?.webContents.send('voice-processing-warning', {
            warning: 'Live voice processing failed, falling back to standard processing',
            error: liveError.message,
            transcript: transcript
          });

          // Fall back to regular Gemini API if Deepgram/Gemini fails
        }
      } else {
        console.log('DEBUG: GeminiLiveService not available or not ready');
        if (deps.geminiLiveService) {
          console.log('DEBUG: GeminiLiveService exists but is not ready');
          console.log('DEBUG: GeminiLiveService ready state:', deps.geminiLiveService.isReady());
        } else {
          console.log('DEBUG: GeminiLiveService is null');
        }
      }

      // Fallback to regular Gemini API through ProcessingHelper
      if (!deps.processingHelper) {
        console.error('DEBUG: Processing helper not initialized');

        // Send error to renderer
        deps.getMainWindow()?.webContents.send('voice-processing-error', {
          error: 'Voice processing service not initialized',
          details: 'Processing helper not available',
          transcript: transcript
        });

        return {
          success: false,
          error: "Processing helper not initialized",
          transcript: recognizedTranscript
        }
      }

      console.log('DEBUG: Using regular Gemini API for voice processing');
      try {
        const response = await deps.processingHelper.processVoiceInput(transcript, language);
        console.log('DEBUG: Regular Gemini API response received, length:', response.length);
        return {
          success: true,
          response,
          transcript: recognizedTranscript
        };
      } catch (processingError) {
        console.error('DEBUG: Error in regular Gemini API processing:', processingError);

        // Send error to renderer
        deps.getMainWindow()?.webContents.send('voice-processing-error', {
          error: 'Error processing voice with standard API',
          details: processingError.message,
          transcript: transcript
        });

        throw processingError;
      }
    } catch (error) {
      console.error("DEBUG: Error processing voice input:", error);
      console.error("DEBUG: Error stack:", error.stack);

      // Send comprehensive error to renderer
      deps.getMainWindow()?.webContents.send('voice-processing-error', {
        error: 'Voice processing failed',
        details: error.message,
        stack: error.stack,
        transcript: transcript
      });

      return {
        success: false,
        error: "Failed to process voice input: " + error.message,
        errorDetails: error.stack,
        transcript: transcript
      }
    }
  })
}



