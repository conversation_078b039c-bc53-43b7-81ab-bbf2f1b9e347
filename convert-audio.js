// convert-audio.js
// This script demonstrates how to convert WebM audio to WAV format
// Note: This requires ffmpeg to be installed on the system

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to convert WebM to WAV using ffmpeg
function convertWebmToWav(inputPath, outputPath) {
  return new Promise((resolve, reject) => {
    // Check if input file exists
    if (!fs.existsSync(inputPath)) {
      reject(new Error(`Input file does not exist: ${inputPath}`));
      return;
    }

    // Create ffmpeg command
    const command = `ffmpeg -i "${inputPath}" -acodec pcm_s16le -ar 44100 -ac 1 "${outputPath}"`;
    
    console.log(`Executing command: ${command}`);
    
    // Execute ffmpeg command
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error executing ffmpeg: ${error.message}`);
        reject(error);
        return;
      }
      
      if (stderr) {
        console.log(`ffmpeg stderr: ${stderr}`);
      }
      
      console.log(`Successfully converted ${inputPath} to ${outputPath}`);
      resolve(outputPath);
    });
  });
}

// Main function
async function main() {
  try {
    // Define paths
    const inputPath = path.join(__dirname, 'sample-audio.webm');
    const outputPath = path.join(__dirname, 'sample-audio-converted.wav');
    
    // Check if input file exists
    if (!fs.existsSync(inputPath)) {
      console.log(`Input file does not exist: ${inputPath}`);
      console.log('Creating a dummy WebM file for testing...');
      
      // Create a dummy WebM file
      const dummyWebmHeader = Buffer.from([
        0x1A, 0x45, 0xDF, 0xA3, // WebM header
        0x01, 0x00, 0x00, 0x00, // Simple WebM-like content
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00
      ]);
      
      fs.writeFileSync(inputPath, dummyWebmHeader);
      console.log(`Created dummy WebM file: ${inputPath}`);
    }
    
    // Convert WebM to WAV
    await convertWebmToWav(inputPath, outputPath);
    
    console.log('Conversion completed successfully.');
  } catch (error) {
    console.error(`Error: ${error.message}`);
  }
}

// Run the main function
main();
