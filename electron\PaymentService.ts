// PaymentService.ts
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { app } from 'electron';

/**
 * Service to handle license validation and payment processing
 */
export class PaymentService {
  private licenseFilePath: string;
  private licenseKey: string | null = null;
  private licenseValid: boolean = false;
  private trialEndDate: Date | null = null;
  private trialActive: boolean = false;
  
  // Secret key for license validation (in a real app, this would be more secure)
  private readonly SECRET_KEY = 'code-genius-license-verification-key';
  
  constructor() {
    // Set up the license file path in the user data directory
    const userDataPath = app.getPath('userData');
    this.licenseFilePath = path.join(userDataPath, 'license.dat');
    
    // Initialize the license state
    this.initializeLicense();
  }
  
  /**
   * Initialize the license state by reading the license file
   */
  private initializeLicense(): void {
    try {
      // Check if license file exists
      if (fs.existsSync(this.licenseFilePath)) {
        // Read and decrypt the license file
        const encryptedData = fs.readFileSync(this.licenseFilePath, 'utf-8');
        const licenseData = this.decryptLicenseData(encryptedData);
        
        if (licenseData) {
          const { key, expiryDate } = licenseData;
          this.licenseKey = key;
          
          // Check if the license is still valid
          const expiry = new Date(expiryDate);
          this.licenseValid = expiry > new Date();
          
          console.log(`License loaded: ${this.licenseKey}, Valid: ${this.licenseValid}`);
        } else {
          console.log('Invalid license data format');
          this.initializeTrial();
        }
      } else {
        console.log('No license file found, initializing trial');
        this.initializeTrial();
      }
    } catch (error) {
      console.error('Error initializing license:', error);
      this.initializeTrial();
    }
  }
  
  /**
   * Initialize the trial period
   */
  private initializeTrial(): void {
    try {
      const trialInfoPath = path.join(app.getPath('userData'), 'trial.dat');
      
      if (fs.existsSync(trialInfoPath)) {
        // Read existing trial info
        const encryptedData = fs.readFileSync(trialInfoPath, 'utf-8');
        const trialData = this.decryptLicenseData(encryptedData);
        
        if (trialData && trialData.startDate) {
          const startDate = new Date(trialData.startDate);
          // Trial period is 7 days
          this.trialEndDate = new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000);
          this.trialActive = this.trialEndDate > new Date();
        } else {
          this.createNewTrial(trialInfoPath);
        }
      } else {
        this.createNewTrial(trialInfoPath);
      }
      
      console.log(`Trial active: ${this.trialActive}, End date: ${this.trialEndDate}`);
    } catch (error) {
      console.error('Error initializing trial:', error);
      // Default to trial active for 7 days from now if there's an error
      this.trialEndDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
      this.trialActive = true;
    }
  }
  
  /**
   * Create a new trial period
   */
  private createNewTrial(trialInfoPath: string): void {
    const startDate = new Date();
    this.trialEndDate = new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000);
    this.trialActive = true;
    
    // Save the trial information
    const trialData = {
      startDate: startDate.toISOString(),
      endDate: this.trialEndDate.toISOString()
    };
    
    const encryptedData = this.encryptLicenseData(trialData);
    fs.writeFileSync(trialInfoPath, encryptedData);
  }
  
  /**
   * Activate a license with the provided key
   */
  public activateLicense(licenseKey: string): boolean {
    try {
      // In a real app, this would validate with a server
      // For this demo, we'll accept any key that's at least 16 characters
      if (licenseKey && licenseKey.length >= 16) {
        // Set license expiry to 1 year from now
        const expiryDate = new Date();
        expiryDate.setFullYear(expiryDate.getFullYear() + 1);
        
        const licenseData = {
          key: licenseKey,
          activationDate: new Date().toISOString(),
          expiryDate: expiryDate.toISOString()
        };
        
        // Encrypt and save the license data
        const encryptedData = this.encryptLicenseData(licenseData);
        fs.writeFileSync(this.licenseFilePath, encryptedData);
        
        // Update the current state
        this.licenseKey = licenseKey;
        this.licenseValid = true;
        this.trialActive = false;
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error activating license:', error);
      return false;
    }
  }
  
  /**
   * Check if the user has a valid license or is in trial period
   */
  public isLicensed(): boolean {
    return this.licenseValid || this.trialActive;
  }
  
  /**
   * Get the license status information
   */
  public getLicenseStatus(): { 
    licensed: boolean; 
    trialActive: boolean; 
    trialDaysLeft: number | null;
    licenseKey: string | null;
  } {
    let trialDaysLeft = null;
    
    if (this.trialActive && this.trialEndDate) {
      const now = new Date();
      const diffTime = this.trialEndDate.getTime() - now.getTime();
      trialDaysLeft = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    
    return {
      licensed: this.licenseValid,
      trialActive: this.trialActive,
      trialDaysLeft,
      licenseKey: this.licenseKey
    };
  }
  
  /**
   * Encrypt license data for storage
   */
  private encryptLicenseData(data: any): string {
    try {
      const iv = crypto.randomBytes(16);
      const key = crypto.scryptSync(this.SECRET_KEY, 'salt', 32);
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
      
      let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      console.error('Encryption error:', error);
      return '';
    }
  }
  
  /**
   * Decrypt license data from storage
   */
  private decryptLicenseData(encryptedData: string): any {
    try {
      const [ivHex, encryptedText] = encryptedData.split(':');
      if (!ivHex || !encryptedText) return null;
      
      const iv = Buffer.from(ivHex, 'hex');
      const key = crypto.scryptSync(this.SECRET_KEY, 'salt', 32);
      const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
      
      let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('Decryption error:', error);
      return null;
    }
  }
}
