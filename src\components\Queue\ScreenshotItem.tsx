// src/components/ScreenshotItem.tsx
import React from "react"
import { X } from "lucide-react"

interface Screenshot {
  path: string
  preview: string
}

interface ScreenshotItemProps {
  screenshot: Screenshot
  onDelete: (index: number) => void
  index: number
  isLoading: boolean
}

const ScreenshotItem: React.FC<ScreenshotItemProps> = ({
  screenshot,
  onDelete,
  index,
  isLoading
}) => {
  const handleDelete = async () => {
    await onDelete(index)
  }

  return (
    <div className={`relative rounded-lg overflow-hidden ${isLoading ? "" : "group"} shadow-md transition-all duration-300 hover:shadow-lg hover-lift animate-fadeIn backdrop-blur-sm`}>
      <div className="aspect-video w-full relative">
        {/* Subtle animated border */}
        <div className="absolute inset-0 rounded-lg border-2 border-blue-500/20 group-hover:border-blue-500/40 transition-colors duration-300"></div>

        {isLoading && (
          <div className="absolute inset-0 bg-black/80 backdrop-blur-sm z-10 flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
            <div className="absolute text-xs text-blue-400 mt-12">Processing...</div>
          </div>
        )}

        <img
          src={screenshot.preview}
          alt="Screenshot"
          className={`w-full h-full object-cover transition-all duration-300 ${
            isLoading
              ? "opacity-50 blur-sm"
              : "cursor-pointer group-hover:scale-105 group-hover:brightness-95"
          }`}
        />

        {/* Overlay with gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Screenshot number badge with glow effect */}
        <div className="absolute top-2 left-2 bg-gradient-to-r from-blue-600 to-blue-500 text-white text-xs font-medium px-2 py-1 rounded-md shadow-md group-hover:shadow-blue-500/50 transition-shadow duration-300">
          #{index + 1}
        </div>

        {!isLoading && (
          <div className="absolute bottom-2 right-2 flex space-x-2">
            {/* View button */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                // This would open a preview in a real implementation
                console.log('View screenshot', screenshot.path)
              }}
              className="p-1.5 rounded-full bg-blue-500 text-white opacity-0 group-hover:opacity-100 transition-all duration-300 hover:bg-blue-600 hover:scale-110 btn-press"
              aria-label="View screenshot"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            </button>

            {/* Delete button */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                handleDelete()
              }}
              className="p-1.5 rounded-full bg-red-500 text-white opacity-0 group-hover:opacity-100 transition-all duration-300 hover:bg-red-600 hover:scale-110 btn-press"
              aria-label="Delete screenshot"
            >
              <X size={16} />
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default ScreenshotItem
