import React, { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { COMMAND_KEY } from '../utils/platform';

interface HeaderProps {
  onOpenLicense: () => void;
  currentLanguage: string;
  setLanguage: (language: string) => void;
  currentView?: "queue" | "solutions" | "debug" | "voice";
  setView?: (view: "queue" | "solutions" | "debug" | "voice") => void;
}

const Header: React.FC<HeaderProps> = ({ onOpenLicense, currentLanguage, setLanguage, currentView = "queue", setView }) => {
  const [licenseStatus, setLicenseStatus] = useState<{
    licensed: boolean;
    trialActive: boolean;
    trialDaysLeft: number | null;
  } | null>(null);

  const [languageMenuOpen, setLanguageMenuOpen] = useState(false);

  const languages = [
    { id: 'javascript', name: 'JavaScript' },
    { id: 'python', name: 'Python' },
    { id: 'java', name: 'Java' },
    { id: 'cpp', name: 'C++' },
    { id: 'csharp', name: 'C#' },
    { id: 'golang', name: 'Go' },
    { id: 'ruby', name: 'Ruby' },
    { id: 'typescript', name: 'TypeScript' },
  ];

  useEffect(() => {
    const getLicenseStatus = async () => {
      try {
        const status = await window.electronAPI.getLicenseStatus();
        setLicenseStatus(status);
      } catch (error) {
        console.error('Error getting license status:', error);
      }
    };

    getLicenseStatus();
  }, []);

  return (
    <header className="bg-gradient-to-r from-gray-900 to-gray-800 border-b border-gray-700 py-3 px-4 flex items-center justify-between">
      <div className="flex items-center">
        <div className="flex items-center mr-6">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <polyline points="16 18 22 12 16 6"></polyline>
            <polyline points="8 6 2 12 8 18"></polyline>
          </svg>
          <h1 className="text-xl font-bold gradient-text gradient-blue">Code Genius</h1>
        </div>

        {/* Navigation Tabs */}
        {setView && (
          <div className="flex items-center mr-6 space-x-1">
            <button
              className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                currentView === 'queue'
                  ? 'bg-blue-600/20 text-blue-300 border border-blue-500/30'
                  : 'text-gray-400 hover:text-gray-200 hover:bg-gray-800'
              }`}
              onClick={() => setView('queue')}
            >
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <circle cx="8.5" cy="8.5" r="1.5"></circle>
                  <polyline points="21 15 16 10 5 21"></polyline>
                </svg>
                <span>Screenshots</span>
              </div>
            </button>

            <button
              className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                currentView === 'voice'
                  ? 'bg-purple-600/20 text-purple-300 border border-purple-500/30'
                  : 'text-gray-400 hover:text-gray-200 hover:bg-gray-800'
              }`}
              onClick={() => setView('voice')}
            >
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" />
                  <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
                  <line x1="12" y1="19" x2="12" y2="23" />
                  <line x1="8" y1="23" x2="16" y2="23" />
                </svg>
                <span>Voice</span>
              </div>
            </button>
          </div>
        )}

        <div className="relative">
          <button
            className="flex items-center space-x-1 bg-gray-800 hover:bg-gray-700 text-gray-200 px-3 py-1.5 rounded-md text-sm transition-colors"
            onClick={() => setLanguageMenuOpen(!languageMenuOpen)}
          >
            <span>Language: {languages.find(l => l.id === currentLanguage)?.name || currentLanguage}</span>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          </button>

          {languageMenuOpen && (
            <div className="absolute top-full left-0 mt-1 bg-gray-800 border border-gray-700 rounded-md shadow-lg z-10 w-40">
              <ul className="py-1">
                {languages.map(language => (
                  <li key={language.id}>
                    <button
                      className={`w-full text-left px-4 py-2 text-sm ${currentLanguage === language.id ? 'bg-blue-600 text-white' : 'text-gray-200 hover:bg-gray-700'}`}
                      onClick={() => {
                        setLanguage(language.id);
                        setLanguageMenuOpen(false);
                      }}
                    >
                      {language.name}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      <div className="flex items-center space-x-3">
        {licenseStatus && (
          <>
            {licenseStatus.licensed ? (
              <div className="badge badge-green">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/>
                </svg>
                <span>Premium</span>
              </div>
            ) : licenseStatus.trialActive ? (
              <div className="badge badge-yellow">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10" />
                  <polyline points="12 6 12 12 16 14" />
                </svg>
                <span>Trial {licenseStatus.trialDaysLeft !== null ? `(${licenseStatus.trialDaysLeft}d)` : ''}</span>
              </div>
            ) : (
              <div className="badge badge-red">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10" />
                  <line x1="12" y1="8" x2="12" y2="12" />
                  <line x1="12" y1="16" x2="12.01" y2="16" />
                </svg>
                <span>Expired</span>
              </div>
            )}
          </>
        )}

        <Button
          onClick={onOpenLicense}
          className="btn btn-premium text-xs px-3 py-1.5"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1.5" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/>
          </svg>
          {licenseStatus?.licensed ? 'Manage License' : 'Activate License'}
        </Button>

        <button
          className="text-gray-400 hover:text-white transition-colors"
          onClick={() => window.electronAPI.toggleMainWindow()}
          title={`Hide window (${COMMAND_KEY}+B)`}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M18 6L6 18M6 6l12 12" />
          </svg>
        </button>
      </div>
    </header>
  );
};

export default Header;
