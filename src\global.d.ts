interface Window {
  electronAPI: {
    // Subscription and payment methods
    openSubscriptionPortal: (authData: {
      id: string;
      email: string;
    }) => Promise<{ success: boolean; error?: string }>;
    updateContentDimensions: (dimensions: {
      width: number;
      height: number;
    }) => Promise<void>;
    clearStore: () => Promise<{ success: boolean; error?: string }>;

    // License and payment methods
    getLicenseStatus: () => Promise<{
      licensed: boolean;
      trialActive: boolean;
      trialDaysLeft: number | null;
      licenseKey: string | null;
      error?: string;
    }>;
    activateLicense: (licenseKey: string) => Promise<{ success: boolean; error?: string }>;
    checkLicense: () => Promise<{ licensed: boolean; error?: string }>;
    onLicenseDialogRequested: (callback: () => void) => () => void;

    // Screenshot methods
    getScreenshots: () => Promise<{
      success: boolean;
      previews?: Array<{ path: string; preview: string }> | null;
      error?: string;
    }>;
    deleteScreenshot: (
      path: string
    ) => Promise<{ success: boolean; error?: string }>;
    onScreenshotTaken: (
      callback: (data: { path: string; preview: string }) => void
    ) => () => void;

    // View and processing events
    onResetView: (callback: () => void) => () => void;
    onSolutionStart: (callback: () => void) => () => void;
    onDebugStart: (callback: () => void) => () => void;
    onDebugSuccess: (callback: (data: any) => void) => () => void;
    onSolutionError: (callback: (error: string) => void) => () => void;
    onProcessingNoScreenshots: (callback: () => void) => () => void;
    onProblemExtracted: (callback: (data: any) => void) => () => void;
    onSolutionSuccess: (callback: (data: any) => void) => () => void;
    onUnauthorized: (callback: () => void) => () => void;
    onDebugError: (callback: (error: string) => void) => () => void;

    // External and window methods
    openExternal: (url: string) => void;
    toggleMainWindow: () => Promise<{ success: boolean; error?: string }>;

    // Trigger methods
    triggerScreenshot: () => Promise<{ success: boolean; error?: string }>;
    triggerProcessScreenshots: () => Promise<{ success: boolean; error?: string }>;
    triggerProcessExtraScreenshots: () => Promise<{ success: boolean; error?: string }>;
    triggerReset: () => Promise<{ success: boolean; error?: string }>;
    triggerMoveLeft: () => Promise<{ success: boolean; error?: string }>;
    triggerMoveRight: () => Promise<{ success: boolean; error?: string }>;
    triggerMoveUp: () => Promise<{ success: boolean; error?: string }>;
    triggerMoveDown: () => Promise<{ success: boolean; error?: string }>;

    // Legacy subscription methods
    onSubscriptionUpdated: (callback: () => void) => () => void;
    onSubscriptionPortalClosed: (callback: () => void) => () => void;

    // Update methods
    startUpdate: () => Promise<{ success: boolean; error?: string }>;
    installUpdate: () => void;
    onUpdateAvailable: (callback: (info: any) => void) => () => void;
    onUpdateDownloaded: (callback: (info: any) => void) => () => void;

    // Credits methods
    decrementCredits: () => Promise<void>;
    onCreditsUpdated: (callback: (credits: number) => void) => () => void;
    onOutOfCredits: (callback: () => void) => () => void;

    // System methods
    getPlatform: () => string;

    // Voice processing methods
    processVoiceInput: (transcript: string, language: string, audioBuffer?: Uint8Array, isUserSpeech?: boolean, resumeInfo?: string, jobDescription?: string) => Promise<{ success: boolean; response?: string; error?: string; transcript?: string }>;

    // Resume and job description methods
    updateResumeJobInfo: (resumeInfo: string, jobDescription: string) => Promise<{ success: boolean; error?: string }>;
  };
  __CREDITS__: number;
  __IS_AUTHENTICATED__: boolean;
  __IS_SUBSCRIBED__: boolean;
}
