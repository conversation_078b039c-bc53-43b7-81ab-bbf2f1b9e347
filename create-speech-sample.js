// create-speech-sample.js
// This script creates a text file with sample speech content that can be used for testing
// The text file can be used with text-to-speech tools to create a proper audio sample

const fs = require('fs');
const path = require('path');

// Sample speech content for testing
const sampleSpeech = `
Hello, this is a test of the speech recognition system.
I am speaking clearly to test the Deepgram API.
The quick brown fox jumps over the lazy dog.
Testing one, two, three, four, five.
This is a sample audio file for transcription testing.
`;

// Write the sample speech to a text file
const sampleTextPath = path.join(__dirname, 'sample-speech.txt');
fs.writeFileSync(sampleTextPath, sampleSpeech);

console.log(`Created sample speech text file: ${sampleTextPath}`);
console.log('To create a proper audio sample:');
console.log('1. Use a text-to-speech tool to convert this text to audio');
console.log('2. Save the audio as "sample-audio.wav" in the project root');
console.log('3. Restart the application to test with the new sample audio');

// Instructions for using ffmpeg to convert text to speech (if available)
console.log('\nIf you have ffmpeg with TTS capabilities installed, you can use:');
console.log('ffmpeg -f lavfi -i "sine=frequency=1000:duration=5" -af "volume=0.1" sample-audio.wav');
console.log('This will create a 5-second audio file with a 1000Hz tone.');

// Instructions for using online TTS services
console.log('\nAlternatively, you can use online text-to-speech services:');
console.log('1. Go to https://ttsmp3.com/ or similar service');
console.log('2. Paste the content of sample-speech.txt');
console.log('3. Generate and download the audio');
console.log('4. Save it as "sample-audio.wav" in the project root');
